# HillOS AI - AI 辅助软件工程体系

基于 HillOS 下一代安全管理和运营产品技术平台，本项目致力于构建**面向 AI 协作、AI 驱动的新一代软件工程体系**，通过深度融合 AI 辅助软件工程技术，显著提升软件开发全生命周期的自动化水平和工程效率。

## 🎯 项目概述

HillOS AI 是一个端到端的 AI 驱动软件工程解决方案，旨在将传统软件开发模式升级为面向 AI 协作的新一代软件工程体系。项目通过规范体系、模板体系和设施体系三大核心技术架构，实现从需求分析到代码生成的全流程 AI 辅助。

### 核心价值主张

- **🚀 20%-30% → 50%-60% 提效跃升**：从简单任务辅助提升到复杂工程项目全流程支持
- **🤖 AI 原生工程方法**：专为 AI 编程助手设计的工程规范和工作流
- **🏗️ 标准化架构驱动**：深度集成 HillOS 技术规范的模块化解决方案
- **📚 知识工程化传承**：将架构经验和最佳实践标准化、模板化

## 📁 项目架构

```
hillos-ai/
├── 📋 PLANNING.md                    # 项目架构与规划
├── 📖 README.md                      # 项目说明文档
├── 📝 CHECKLIST.md                   # 技术规范验收清单
├── 🔧 lombok.config                  # Lombok 配置
├── 📚 docs/                          # 项目文档与技术规范
│   ├── guidelines/HSR/               # HillOS 技术规范文档
│   │   ├── HillOS日志打印开发指南.md
│   │   ├── HillOS总体架构设计.md
│   │   ├── HSR01 HillOS模块开发技术规范.md
│   │   ├── HSR04 HillOS模块CICD集成规范.md
│   │   └── HSR05 HillOS模块REST接口规范.md
│   └── templates/                    # 设计文档模板
│       ├── FD.md                     # 功能设计文档模板
│       └── PLANNING.md               # 项目规划文档模板
├── 🏗️ hillos-archetype/             # Maven Archetype 代码骨架
│   ├── pom.xml                       # Archetype 项目配置
│   └── src/main/resources/
│       ├── META-INF/
│       └── archetype-resources/      # 项目模板资源
│           ├── interface/            # 接口层模板
│           ├── framework/            # 框架层模板
│           └── server/               # 服务层模板
├── 🧪 hillos-demo-application/       # 完整应用项目示例
│   ├── interface/                    # REST接口契约、DTOs、Facade
│   ├── framework/                    # 实体、Repository、基础设施
│   ├── server/                       # Controller、Service、启动类
│   ├── CHECKLIST.md                  # 规范验收清单
│   ├── docker-compose.yml            # 容器编排配置
│   └── sonar-project.properties      # 代码质量检查配置
├── 📦 hillos-demo-starter/           # Starter 组件示例
└── 🎯 hillos-prompts/                # AI 工程规范提示词平台
    ├── docs/                         # 技术架构文档
    │   ├── guidelines/HSR/           # HillOS 规范文档
    │   ├── keynotes/                 # 核心理念介绍
    │   └── templates/                # 文档模板
    ├── prompts/                      # 工程规范提示词
    │   ├── project/                  # 项目初始化提示词
    │   │   ├── ai_init.md            # AI助手规则初始化
    │   │   ├── ai_check.md           # 项目规范验收
    │   │   ├── project_init.md       # 项目框架初始化
    │   │   └── project_init/         # 模块化项目初始化
    │   └── rules/                    # 规范与规则
    │       ├── assistant/            # AI 助手行为规则
    │       └── guideline/            # 技术栈规范指南
    └── test/                         # 测试验证项目
```

## 🏗️ 核心架构

### 技术体系架构

HillOS AI 基于三个核心技术体系构建，形成完整的 AI 辅助开发解决方案：

#### 1. 规范体系 (Standards System)
- **核心作用**：构建完整的 AI 协作软件开发生命周期规范
- **主要内容**：
  - AI 工程规范库 (HillOS 技术规范文档)
  - 设计检查清单 (CHECKLIST.md)
  - AI 协作指南和提示语工程规范
  - 代码质量标准 (SonarQube 配置)

#### 2. 模板体系 (Template System)
- **核心作用**：开发从设计到实现的全套智能化工程模板
- **主要内容**：
  - `hillos-archetype`: Maven Archetype 代码骨架模板
  - `docs/templates`: 文档模板 (FD.md, PLANNING.md)
  - `hillos-demo-*`: 完整项目示例和参考实现
  - 可被 AI 理解和运用的标准代码结构

#### 3. 设施体系 (Facility System)
- **核心作用**：构建软件工程自动化的核心指令集和基础设施
- **主要内容**：
  - `hillos-prompts`: AI 编程助手工程规范提示词平台
  - 标准化工程指令集 (项目初始化、代码生成等)
  - AI 模型管理和工作流编排能力
  - 与大模型应用平台的深度集成能力

### 信息流架构

```mermaid
graph TD
    subgraph "输入层 (Input Layer)"
        A[产品需求 / PRD] --> C{AI工程设施}
        B[标准化工程指令] --> C
    end

    subgraph "AI处理层 (AI Processing Layer)"
        C --> D{AI辅助工作流}
        E[AI工程规范库] -.-> D
        F[智能化模板库] -.-> D
        D --> G[设计文档 PRD/FD]
        D --> H[应用示例 Demo/Samples]
    end

    subgraph "输出层 (Output Layer)"
        H --> I{模板固化}
        I --> J[代码骨架/脚手架]
        G --> K[规范化文档资产]
    end

    subgraph "反馈层 (Feedback Layer)"
        J --> L[持续改进]
        K --> L
        L -.-> E
        L -.-> F
    end
```

## 🚀 快速开始 - AI 辅助软件工程流程

### 完整 AI 辅助软件工程指令流程

#### 1. **ai_init 指令** - AI助手规则初始化
```bash
# 使用 hillos-prompts/prompts/project/ai_init.md
# 目标：为 AI 助手配置统一的项目开发规范
```
**功能说明**：
- 自动配置多个 AI 工具的项目规则 (Lingma, Roo, Copilot, Cursor, Continue)
- 同步 HillOS 技术规范到各 AI 助手配置目录
- 确保所有 AI 工具使用统一的开发规范

#### 2. **project_init 指令** - 项目框架初始化
```bash
# 使用 hillos-prompts/prompts/project/project_init.md
# 或者使用 Maven Archetype
mvn archetype:generate \
    -DgroupId=com.hillstone.hillos \
    -DartifactId=hillos-my-project \
    -Dversion=2.0.0-SNAPSHOT \
    -DarchetypeGroupId=com.hillstone.hillos \
    -DarchetypeArtifactId=hillos-archetype
```
**功能说明**：
- 支持三种项目类型：Application (完整应用)、Starter (组件)、Jar (工具库)
- 自动生成符合 HillOS 规范的项目结构
- 包含完整的 Maven 配置、代码模板、测试框架

#### 3. **ai_check 指令** - 项目初始检查
```bash
# 使用 hillos-prompts/prompts/project/ai_check.md
# 目标：自动化项目规范验收
```
**功能说明**：
- 基于 HillOS 技术规范进行全面项目检查
- 覆盖工程规范、框架规范、接口规范、测试规范、安全规范
- 自动生成验收报告和改进建议

#### 4. **project_plan 指令** - 项目概要规划
```bash
# 基于 docs/templates/PLANNING.md 模板
# 目标：生成项目架构和开发规划
```
**功能说明**：
- 项目目标与技术架构设计
- 关键组件和模块划分
- 开发工作流与标准制定
- 技术栈选型和依赖管理

#### 5. **project_fd 指令** - FD详细设计
```bash
# 基于 docs/templates/FD.md 模板
# 目标：生成功能设计文档
```
**功能说明**：
- 详细的功能设计和接口定义
- API 规范和数据模型设计
- 业务流程和异常处理
- 性能和安全要求

#### 6. **project_task 指令** - 任务分解和敏捷开发
```bash
# 基于项目规划进行任务分解
# 目标：支持敏捷开发和迭代管理
```
**功能说明**：
- 将大功能分解为可执行的小任务
- 任务优先级和依赖关系管理
- 迭代计划和里程碑设置
- 团队协作和进度跟踪

#### 7. **project_test 指令** - 总体测试和验收
```bash
# 基于测试规范进行全面测试
# 目标：确保项目质量和交付标准
```
**功能说明**：
- 单元测试和集成测试执行
- 代码覆盖率检查 (目标 ≥60%)
- 性能测试和安全测试
- SonarQube 质量门禁检查

#### 8. **project_retro 指令** - 项目汇总
```bash
# 项目回顾和总结
# 目标：沉淀经验和持续改进
```
**功能说明**：
- 项目交付物汇总和归档
- 开发过程回顾和经验总结
- 问题和改进点识别
- 最佳实践和规范更新

### 5分钟快速体验

1. **获取项目**
   ```bash
   git clone <项目仓库地址>
   cd hillos-ai
   ```

2. **查看核心AI工程指令**
   ```bash
   # 查看AI助手初始化指令
   cat hillos-prompts/prompts/project/ai_init.md

   # 查看项目初始化指令
   cat hillos-prompts/prompts/project/project_init.md

   # 查看项目检查指令
   cat hillos-prompts/prompts/project/ai_check.md
   ```

3. **运行示例应用**
   ```bash
   cd hillos-demo-application
   mvn clean install
   java -jar server/target/hillos-demo-server-2.0.0-SNAPSHOT.jar
   ```

4. **访问 API 文档**
   ```
   http://localhost:8080/swagger-ui.html
   ```

### 按角色使用指南

#### 👨‍💻 开发工程师
1. **配置AI助手**：使用 `ai_init` 指令导入项目规则
2. **初始化项目**：使用 `project_init` 指令或 Maven Archetype
3. **规范检查**：使用 `ai_check` 指令进行代码规范验证

#### 🏗️ 架构师
1. **项目规划**：使用 `project_plan` 指令进行架构设计
2. **功能设计**：使用 `project_fd` 指令编写详细设计
3. **质量把控**：使用 `project_test` 指令确保交付质量

#### 🤖 AI 助手用户
1. **规则初始化**：执行 `ai_init` 指令配置开发规范
2. **流程化开发**：按照 8 个指令的顺序进行项目开发
3. **持续改进**：使用 `project_retro` 指令总结和优化

## 💡 核心功能特性

### 🎯 AI 辅助工程化指令集
- **8 个标准化指令**：覆盖从项目初始化到交付汇总的完整流程
- **提示语工程**：专为 AI 编程助手设计的结构化指令
- **规范驱动**：深度集成 HillOS 技术规范的 AI 协作模式

### 📚 模块化架构体系
- **分层架构**：Interface (接口契约) → Framework (基础设施) → Server (业务逻辑)
- **职责分离**：每个模块专注单一职责，便于维护和扩展
- **可组合性**：支持根据需要组合不同的功能模块

### 🔧 AI 协作优化
- **上下文管理**：项目规则、状态记忆、MCP 协议扩展
- **任务分解**：需求分析 → 任务拆解 → 编排执行 → 自测修复
- **闭环反馈**：测试生成 → 验证运行 → 错误修复 → 文档更新
- **多工具集成**：支持 Lingma、Roo、Copilot、Cursor、Continue 等主流 AI 助手

## 🛠️ 技术规范

### AI 辅助工程化技术栈

| 技术领域                | 核心技术                        | 版本   | 说明                   |
| ----------------------- | ------------------------------- | ------ | ---------------------- |
| **提示语工程**          | CRISPE 框架                     | Latest | 结构化提示词生成和管理 |
|                         | 模块化提示词                    | Latest | 可复用的工程指令模块   |
|                         | 上下文管理                      | Latest | 项目规则和状态记忆     |
| **AI 辅助工程化指令**   | ai_init                         | Latest | AI助手规则初始化       |
|                         | project_init                    | Latest | 项目框架初始化         |
|                         | ai_check                        | Latest | 项目规范验收           |
|                         | project_plan/fd/task/test/retro | Latest | 全流程工程指令         |
| **AI 辅助软件工程流程** | 需求分析                        | Latest | PRD/FD 文档生成        |
|                         | 代码生成                        | Latest | 符合规范的代码模板     |
|                         | 测试验证                        | Latest | 自动化测试和质量检查   |
|                         | 持续改进                        | Latest | 经验沉淀和规范优化     |
| **平台集成**            | Java                            | 1.8+   | 编译和运行环境         |
|                         | Spring Boot                     | 2.7.9  | 微服务框架             |
|                         | Maven Archetype                 | 3.2.1  | 项目模板生成           |
| **质量保证**            | SonarQube                       | Latest | 代码质量检查           |
|                         | JaCoCo                          | Latest | 代码覆盖率 ≥60%        |
|                         | JUnit 5 + Mockito               | Latest | 单元测试框架           |

### 开发标准
- **AI 工程规范**：严格遵循 [AI助手项目规则](hillos-prompts/prompts/project/ai_init.md)
- **架构模式**：DDD 分层架构 + 微服务设计
- **接口规范**：遵循 [HillOS REST接口规范](docs/guidelines/HSR/HSR05%20HillOS模块REST接口规范.md)
- **测试要求**：单元测试覆盖率 > 60%，集成测试完整

### 质量保证
- **AI 辅助验收**：基于 [CHECKLIST.md](CHECKLIST.md) 的自动化验收
- **持续集成**：Jenkins + SonarQube 代码质量检查
- **版本管理**：基于 `${revision}` 的统一版本管理
- **多工具协同**：确保各 AI 助手使用统一规范

## 📊 使用示例

### AI 辅助项目完整开发流程

#### 步骤 1：初始化 AI 助手环境
```markdown
# 使用 ai_init 指令
请执行 AI 助手规则初始化，为当前项目配置统一的 HillOS 开发规范。

确保以下 AI 工具都使用相同的项目规则：
- Lingma (.lingma/rules/project_rule.md)
- Roo (.roo/rules/project_rule.md)
- Copilot (.github/copilot-instructions.md)
- Cursor (.cursor/rules/project-rule.mdc)
- Continue (.continue/rules/project_rule.md)
```

#### 步骤 2：创建新项目
```markdown
# 使用 project_init 指令
请基于 HillOS 规范创建一个新的 application 类型项目：

项目信息：
- 项目名称：hillos-user-service
- 项目描述：HillOS 用户管理微服务
- 包名基础：com.hillstone.hillos.user
- 项目类型：application

请按照模块化流程执行项目初始化。
```

#### 步骤 3：项目规范检查
```markdown
# 使用 ai_check 指令
请对刚创建的 hillos-user-service 项目进行全面的规范验收检查。

检查内容包括：
- 工程规范（命名、版本、包结构、项目文档）
- 框架规范（运行环境、中间件、服务层框架）
- 接口规范（REST API、消息总线）
- 测试规范（单元测试、集成测试、覆盖率）
- 安全规范（输入验证、敏感数据处理）

请生成详细的验收报告。
```

### 使用 Maven Archetype 快速创建

```bash
mvn archetype:generate \
    -DgroupId=com.hillstone.hillos \
    -DartifactId=hillos-my-project \
    -Dversion=2.0.0-SNAPSHOT \
    -DarchetypeGroupId=com.hillstone.hillos \
    -DarchetypeArtifactId=hillos-archetype \
    -DarchetypeVersion=2.0.0-SNAPSHOT
```

### 配置 AI 助手规则

将 [`hillos-prompts/prompts/project/ai_init.md`](hillos-prompts/prompts/project/ai_init.md) 中的指令提供给您的 AI 编程助手，即可自动配置符合 HillOS 规范的开发环境。

## 🔗 相关文档

- **项目规划**：[PLANNING.md](PLANNING.md) - 完整的项目架构与规划
- **技术规范**：[docs/guidelines/HSR/](docs/guidelines/HSR/) - HillOS 技术规范文档
- **AI 工程指令**：[hillos-prompts/prompts/project/](hillos-prompts/prompts/project/) - 完整的 AI 辅助工程指令集
- **规范验收**：[CHECKLIST.md](CHECKLIST.md) - 技术规范验收清单
- **应用示例**：[hillos-demo-application/README.md](hillos-demo-application/README.md) - 完整应用项目示例

## 📈 项目状态

- **当前版本**：1.0.0-SNAPSHOT (持续更新中)
- **技术规范版本**：基于 HillOS R3 版本 (2024-06)
- **支持的项目类型**：Application、Starter、Jar
- **支持的 AI 助手**：Lingma、Roo、Copilot、Cursor、Continue 等
- **AI 工程指令**：8 个标准化指令覆盖完整开发流程

---

*基于 HillOS 技术体系构建，为 AI 编程助手提供专业的软件工程化支持。*


