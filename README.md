# HillOS Prompts: 面向 HillOS 平台的 AI 工程指令框架

## 📌 Overview 简介

**HillOS Prompts** 是一个面向 **HillOS 产品技术平台** 的 AI 工程指令框架。

`HillOS` 作为一个可复用的产品技术平台，其核心是持续提升产品研发效率。为顺应 AI 编程技术的发展趋势，`HillOS` 的工程化体系正向全面的 AI 辅助开发模式演进。

本项目是实现这一工程化升级的核心载体。它将 `HillOS` 的架构规范、开发流程和最佳实践，封装为一系列结构化的 AI 指令，旨在显著提升 `HillOS` 平台的开发自动化水平与工程效率。

## ✨ Features 特性

### 1. 覆盖开发全流程的工程化指令
我们提供了一系列标准化的 AI 指令，旨在将软件开发从需求、设计到测试的各个环节自动化。核心指令包括：
- **`project_init`**: 自动化创建项目骨架。
- **`ai_check`**: 辅助进行项目规范检查。
- **未来规划**: 将逐步覆盖功能设计、任务分解、测试复盘等更多环节。

### 2. 内置的 HillOS 工程规范
所有指令都深度集成了 `HillOS` 的架构模式、编码规范和最佳实践。这确保了 AI 的所有产出都符合团队的技术标准，让规范不再仅仅是文档，而是可被 AI 执行和验证的动作。

### 3. 便捷的 AI 助手集成
本指令框架被设计为可以轻松地与主流 AI 编程助手（如 RooCode, Continue 等）集成。开发者可以在自己熟悉的工具中，方便地调用这些强大的工程化能力，赋能日常开发工作。

## 🚀 Getting Started 入门

### 步骤 1: 配置 AI 助手开发环境
第一步是配置您的 AI 助手，使其遵循统一的 `HillOS` 开发规范。这体现了本框架便捷的集成能力。
```
AI 助手 ai_init
请执行 AI 助手规则初始化，为当前项目配置统一的 HillOS 开发规范。
```

### 步骤 2: 执行项目创建指令
配置完成后，即可调用工程化指令来执行具体任务。以下示例将创建一个完全符合 `HillOS` 内置规范的新项目。
```
AI 助手 project_init
请基于 HillOS 规范创建一个新的 application 类型项目：

项目信息：
- 项目名称：hillos-demo
- 项目描述：HillOS 用户管理微服务
- 包名基础：com.hillstone.hillos.demo
- 项目类型：application

请按照模块化流程执行项目初始化。
```

## 🏗️ Architecture 架构

### 提示词编排架构
为了可靠地执行复杂的工程化指令，我们采用了“分而治之”的设计思想。以 `project_init` 指令为例，项目创建任务被分解为五个独立的阶段，由一个主控提示词进行调度，保证了流程的清晰、稳定和可扩展。

```mermaid
graph TD
    A[主控提示词: project_init.md] --> B(阶段一: 准备);
    A --> C(阶段二: 文件创建);
    A --> D(阶段三: 类型特化);
    A --> E(阶段四: 文档生成);
    A --> F(阶段五: 验证);

    subgraph 阶段三: 类型特化
        D -- 选择一种 --> D1[Application];
        D -- 选择一种 --> D2[Starter];
        D -- 选择一种 --> D3[Jar];
    end
```

### 工程目录结构
项目的目录结构经过精心设计，以支撑指令和规范的分离。`prompts/` 目录是所有工程化指令的实现载体，而 `docs/` 和 `prompts/rules/` 目录则共同构成了内置的规范库。
```plaintext
hillos-prompts/
├── README.md                 # 项目说明文档
├── TASK.md                   # 任务管理与进度追踪
├── docs/                     # HillOS 相关技术规范
│   ├── guidelines/           # HillOS 架构与开发规范
│   └── templates/            # 设计文档模板
├── prompts/                  # 核心提示词指令目录
│   ├── project/              # 项目级任务指令
│   │   └── project_init/     # **推荐**: 模块化的项目初始化指令
│   └── rules/                # 全局规则与约束
└── test/                     # 用于存放和验证AI生成的项目
```

## 📦 Deployment 部署说明

本项目的“部署”即是将标准化的 **AI 工程指令集** 有效**集成**到您的 `HillOS` 开发工作流中。

### 1. 配置全局开发规范
将 `prompts/rules/assistant/assistant_rule.md` 等全局规则设置为 AI 助手的系统提示 (System Prompt)。这使得内置的工程规范能在 AI 处理所有 `HillOS` 相关任务时全局生效。

### 2. 调用任务级工程指令
在执行具体开发任务时，调用相应的任务指令（如 `project_init`, `ai_check`）。AI 助手会加载并执行本仓库中对应的工程化指令，以自动化完成任务。