package com.hillstone.hillos.demo.server.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hillstone.hillos.demo.iface.CreateFooItemRequest;
import com.hillstone.hillos.demo.iface.FooItemDto;
import com.hillstone.hillos.demo.iface.UpdateFooItemRequest;
import com.hillstone.hillos.demo.server.service.FooService;
import com.hillstone.hillos.rest.request.Query;
import com.hillstone.hillos.rest.response.RestQueryResult;
import com.hillstone.hillos.rest.response.RestResult;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(FooController.class)
class FooControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private FooService fooService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @DisplayName("Should return all foo items")
    void shouldReturnAllFooItems() throws Exception {
        // Given
        FooItemDto foo1 = new FooItemDto();
        foo1.setId(1L);
        foo1.setName("Foo 1");
        foo1.setDescription("Description 1");

        FooItemDto foo2 = new FooItemDto();
        foo2.setId(2L);
        foo2.setName("Foo 2");
        foo2.setDescription("Description 2");

        List<FooItemDto> foos = Arrays.asList(foo1, foo2);

        RestQueryResult<List<FooItemDto>> result = RestQueryResult.<List<FooItemDto>>builder()
                .code(HttpStatus.OK.value())
                .message("Success")
                .result(foos)
                .total(2L)
                .build();

        // When
        when(fooService.getAllFoos(any(Query.class))).thenReturn(result);

        // Then
        mockMvc.perform(get("/api/v1/foos")
                        .param("start", "0")
                        .param("limit", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.result", hasSize(2)))
                .andExpect(jsonPath("$.result[0].id", is(1)))
                .andExpect(jsonPath("$.result[0].name", is("Foo 1")))
                .andExpect(jsonPath("$.result[1].id", is(2)))
                .andExpect(jsonPath("$.result[1].name", is("Foo 2")))
                .andExpect(jsonPath("$.total", is(2)));
    }

    @Test
    @DisplayName("Should return a foo item by id")
    void shouldReturnFooById() throws Exception {
        // Given
        Long fooId = 1L;
        FooItemDto foo = new FooItemDto();
        foo.setId(fooId);
        foo.setName("Test Foo");
        foo.setDescription("Test Description");

        RestResult<FooItemDto> result = RestResult.<FooItemDto>builder()
                .code(HttpStatus.OK.value())
                .message("Success")
                .result(foo)
                .build();

        // When
        when(fooService.getFooById(fooId)).thenReturn(result);

        // Then
        mockMvc.perform(get("/api/v1/foos/{id}", fooId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.result.id", is(1)))
                .andExpect(jsonPath("$.result.name", is("Test Foo")))
                .andExpect(jsonPath("$.result.description", is("Test Description")));
    }

    @Test
    @DisplayName("Should create a new foo item")
    void shouldCreateFoo() throws Exception {
        // Given
        CreateFooItemRequest request = new CreateFooItemRequest();
        request.setName("New Foo");
        request.setDescription("New Description");

        FooItemDto createdFoo = new FooItemDto();
        createdFoo.setId(1L);
        createdFoo.setName(request.getName());
        createdFoo.setDescription(request.getDescription());

        RestResult<FooItemDto> result = RestResult.<FooItemDto>builder()
                .code(HttpStatus.CREATED.value())
                .message("Created successfully")
                .result(createdFoo)
                .build();

        // When
        when(fooService.createFoo(any(CreateFooItemRequest.class))).thenReturn(result);

        // Then
        mockMvc.perform(post("/api/v1/foos")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(201)))
                .andExpect(jsonPath("$.result.id", is(1)))
                .andExpect(jsonPath("$.result.name", is("New Foo")))
                .andExpect(jsonPath("$.result.description", is("New Description")));
    }

    @Test
    @DisplayName("Should update an existing foo item")
    void shouldUpdateFoo() throws Exception {
        // Given
        Long fooId = 1L;
        UpdateFooItemRequest request = new UpdateFooItemRequest();
        request.setName("Updated Foo");
        request.setDescription("Updated Description");

        FooItemDto updatedFoo = new FooItemDto();
        updatedFoo.setId(fooId);
        updatedFoo.setName(request.getName());
        updatedFoo.setDescription(request.getDescription());

        RestResult<FooItemDto> result = RestResult.<FooItemDto>builder()
                .code(HttpStatus.OK.value())
                .message("Updated successfully")
                .result(updatedFoo)
                .build();

        // When
        when(fooService.updateFoo(eq(fooId), any(UpdateFooItemRequest.class))).thenReturn(result);

        // Then
        mockMvc.perform(put("/api/v1/foos/{id}", fooId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.result.id", is(1)))
                .andExpect(jsonPath("$.result.name", is("Updated Foo")))
                .andExpect(jsonPath("$.result.description", is("Updated Description")));
    }

    @Test
    @DisplayName("Should delete a foo item")
    void shouldDeleteFoo() throws Exception {
        // Given
        Long fooId = 1L;
        RestResult<Void> result = RestResult.<Void>builder()
                .code(HttpStatus.OK.value())
                .message("Deleted successfully")
                .build();

        // When
        when(fooService.deleteFoo(fooId)).thenReturn(result);

        // Then
        mockMvc.perform(delete("/api/v1/foos/{id}", fooId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(200)))
                .andExpect(jsonPath("$.message", is("Deleted successfully")));
    }
}
