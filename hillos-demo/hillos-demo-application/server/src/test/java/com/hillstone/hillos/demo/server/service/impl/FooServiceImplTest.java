package com.hillstone.hillos.demo.server.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;

import com.hillstone.hillos.demo.framework.domain.entity.FooItem;
import com.hillstone.hillos.demo.framework.exception.ResourceNotFoundException;
import com.hillstone.hillos.demo.framework.repository.FooItemRepository;
import com.hillstone.hillos.demo.iface.CreateFooItemRequest;
import com.hillstone.hillos.demo.iface.FooItemDto;
import com.hillstone.hillos.demo.iface.UpdateFooItemRequest;
import com.hillstone.hillos.rest.request.Query;
import com.hillstone.hillos.rest.response.RestQueryResult;
import com.hillstone.hillos.rest.response.RestResult;

@ExtendWith(MockitoExtension.class)
class FooServiceImplTest {

    @Mock
    private FooItemRepository fooItemRepository;

    @InjectMocks
    private FooServiceImpl fooService;

    private FooItem fooItem1;
    private FooItem fooItem2;
    private Query query;

    @BeforeEach
    void setUp() {
        // Set up test data
        fooItem1 = new FooItem();
        fooItem1.setId(1L);
        fooItem1.setName("Test Foo 1");
        fooItem1.setDescription("Test Description 1");

        fooItem2 = new FooItem();
        fooItem2.setId(2L);
        fooItem2.setName("Test Foo 2");
        fooItem2.setDescription("Test Description 2");

        query = new Query();
        query.setStart(0);
        query.setLimit(10);
    }

    @Test
    @DisplayName("Should get all foos with pagination")
    void shouldGetAllFoosWithPagination() {
        // Given
        List<FooItem> fooItems = Arrays.asList(fooItem1, fooItem2);
        Page<FooItem> page = new PageImpl<>(fooItems);

        when(fooItemRepository.findAll(any(Pageable.class))).thenReturn(page);

        // When
        RestQueryResult<List<FooItemDto>> result = fooService.getAllFoos(query);

        // Then
        assertNotNull(result);
        assertEquals(HttpStatus.OK.value(), result.getCode());
        assertEquals(2, result.getResult().size());
        assertEquals(2L, result.getTotal());
        assertEquals("Test Foo 1", result.getResult().get(0).getName());
        assertEquals("Test Foo 2", result.getResult().get(1).getName());

        verify(fooItemRepository).findAll(any(Pageable.class));
    }

    @Test
    @DisplayName("Should get foo by id")
    void shouldGetFooById() {
        // Given
        when(fooItemRepository.findById(1L)).thenReturn(Optional.of(fooItem1));

        // When
        RestResult<FooItemDto> result = fooService.getFooById(1L);

        // Then
        assertNotNull(result);
        assertEquals(HttpStatus.OK.value(), result.getCode());
        assertEquals("Test Foo 1", result.getResult().getName());
        assertEquals("Test Description 1", result.getResult().getDescription());

        verify(fooItemRepository).findById(1L);
    }

    @Test
    @DisplayName("Should throw exception when foo not found")
    void shouldThrowExceptionWhenFooNotFound() {
        // Given
        when(fooItemRepository.findById(999L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> {
            fooService.getFooById(999L);
        });

        verify(fooItemRepository).findById(999L);
    }

    @Test
    @DisplayName("Should create foo")
    void shouldCreateFoo() {
        // Given
        CreateFooItemRequest request = new CreateFooItemRequest();
        request.setName("New Foo");
        request.setDescription("New Description");

        FooItem newFooItem = new FooItem();
        newFooItem.setName("New Foo");
        newFooItem.setDescription("New Description");

        FooItem savedFooItem = new FooItem();
        savedFooItem.setId(3L);
        savedFooItem.setName("New Foo");
        savedFooItem.setDescription("New Description");

        when(fooItemRepository.save(any(FooItem.class))).thenReturn(savedFooItem);

        // When
        RestResult<FooItemDto> result = fooService.createFoo(request);

        // Then
        assertNotNull(result);
        assertEquals(HttpStatus.CREATED.value(), result.getCode());
        assertEquals(3L, result.getResult().getId());
        assertEquals("New Foo", result.getResult().getName());
        assertEquals("New Description", result.getResult().getDescription());

        verify(fooItemRepository).save(any(FooItem.class));
    }

    @Test
    @DisplayName("Should update foo")
    void shouldUpdateFoo() {
        // Given
        UpdateFooItemRequest request = new UpdateFooItemRequest();
        request.setName("Updated Foo");
        request.setDescription("Updated Description");

        FooItem existingFooItem = fooItem1;
        FooItem updatedFooItem = new FooItem();
        updatedFooItem.setId(1L);
        updatedFooItem.setName("Updated Foo");
        updatedFooItem.setDescription("Updated Description");

        when(fooItemRepository.findById(1L)).thenReturn(Optional.of(existingFooItem));
        when(fooItemRepository.save(any(FooItem.class))).thenReturn(updatedFooItem);

        // When
        RestResult<FooItemDto> result = fooService.updateFoo(1L, request);

        // Then
        assertNotNull(result);
        assertEquals(HttpStatus.OK.value(), result.getCode());
        assertEquals("Updated Foo", result.getResult().getName());
        assertEquals("Updated Description", result.getResult().getDescription());

        verify(fooItemRepository).findById(1L);
        verify(fooItemRepository).save(any(FooItem.class));
    }

    @Test
    @DisplayName("Should throw exception when updating non-existent foo")
    void shouldThrowExceptionWhenUpdatingNonExistentFoo() {
        // Given
        UpdateFooItemRequest request = new UpdateFooItemRequest();
        request.setName("Updated Foo");
        request.setDescription("Updated Description");

        when(fooItemRepository.findById(999L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> {
            fooService.updateFoo(999L, request);
        });

        verify(fooItemRepository).findById(999L);
        verify(fooItemRepository, never()).save(any(FooItem.class));
    }

    @Test
    @DisplayName("Should delete foo")
    void shouldDeleteFoo() {
        // Given
        when(fooItemRepository.existsById(1L)).thenReturn(true);
        doNothing().when(fooItemRepository).deleteById(1L);

        // When
        RestResult<Void> result = fooService.deleteFoo(1L);

        // Then
        assertNotNull(result);
        assertEquals(HttpStatus.OK.value(), result.getCode());

        verify(fooItemRepository).existsById(1L);
        verify(fooItemRepository).deleteById(1L);
    }

    @Test
    @DisplayName("Should throw exception when deleting non-existent foo")
    void shouldThrowExceptionWhenDeletingNonExistentFoo() {
        // Given
        when(fooItemRepository.existsById(999L)).thenReturn(false);

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> {
            fooService.deleteFoo(999L);
        });

        verify(fooItemRepository).existsById(999L);
        verify(fooItemRepository, never()).deleteById(any());
    }
}
