package com.hillstone.hillos.demo.server.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.hillstone.hillos.demo.framework.exception.ResourceNotFoundException;
import com.hillstone.hillos.rest.response.RestResult;

/**
 * 全局异常处理器
 * 负责将服务端异常转换为标准 HTTP 响应
 */
@ControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * 处理 ResourceNotFoundException，返回 404 NOT FOUND 响应
     *
     * @param ex 资源未找到异常
     * @return 包含错误信息的 RestResult
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    @ResponseBody
    public RestResult<Void> handleResourceNotFoundException(ResourceNotFoundException ex) {
        return RestResult.<Void>builder()
                .code(HttpStatus.NOT_FOUND.value())
                .message(ex.getMessage())
                .build();
    }
    
    /**
     * 处理所有未捕获的异常，返回 500 INTERNAL SERVER ERROR 响应
     *
     * @param ex 未处理的异常
     * @return 包含错误信息的 RestResult
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ResponseBody
    public RestResult<Void> handleGeneralException(Exception ex) {
        return RestResult.<Void>builder()
                .code(HttpStatus.INTERNAL_SERVER_ERROR.value())
                .message("An unexpected error occurred: " + ex.getMessage())
                .build();
    }
}
