package com.hillstone.hillos.demo.server.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hillstone.hillos.demo.framework.domain.entity.FooItem;
import com.hillstone.hillos.demo.framework.exception.ResourceNotFoundException;
import com.hillstone.hillos.demo.framework.repository.FooItemRepository;
import com.hillstone.hillos.demo.iface.CreateFooItemRequest;
import com.hillstone.hillos.demo.iface.FooItemDto;
import com.hillstone.hillos.demo.iface.UpdateFooItemRequest;
import com.hillstone.hillos.demo.server.service.FooService;
import com.hillstone.hillos.rest.request.Query;
import com.hillstone.hillos.rest.response.RestQueryResult;
import com.hillstone.hillos.rest.response.RestResult;

/**
 * Implementation of the FooService interface
 */
@Service
public class FooServiceImpl implements FooService {

    private final FooItemRepository fooItemRepository;

    @Autowired
    public FooServiceImpl(FooItemRepository fooItemRepository) {
        this.fooItemRepository = fooItemRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public RestQueryResult<List<FooItemDto>> getAllFoos(Query query) {
        // Extract pagination parameters from Query object
        int page = query.getStart() / query.getLimit(); // Calculate page number (0-based)
        int size = query.getLimit(); // Page size

        // Create pageable object with sort if needed
        Sort sort = Sort.unsorted(); // Default to unsorted, can be enhanced to parse
                                     // query.getSorts()
        Pageable pageable = PageRequest.of(page, size, sort);

        // Execute paged query
        Page<FooItem> fooItemPage = fooItemRepository.findAll(pageable);

        // Convert entities to DTOs
        List<FooItemDto> fooItemDtos = fooItemPage.getContent().stream().map(this::convertToDto)
                .collect(Collectors.toList());

        // Build standard response
        return RestQueryResult.<List<FooItemDto>>builder().code(HttpStatus.OK.value())
                .message("Success").result(fooItemDtos).total(fooItemPage.getTotalElements())
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public RestResult<FooItemDto> getFooById(Long id) {
        FooItem fooItem = fooItemRepository.findById(id)
                .orElseThrow(() -> ResourceNotFoundException.forResource("FooItem", id));

        FooItemDto dto = convertToDto(fooItem);

        return RestResult.<FooItemDto>builder().code(HttpStatus.OK.value()).message("Success")
                .result(dto).build();
    }

    @Override
    @Transactional
    public RestResult<FooItemDto> createFoo(CreateFooItemRequest request) {
        // Create a new entity from the request
        FooItem fooItem = new FooItem();
        fooItem.setName(request.getName());
        fooItem.setDescription(request.getDescription());

        // Save the entity
        FooItem savedItem = fooItemRepository.save(fooItem);

        // Convert to DTO and return
        FooItemDto dto = convertToDto(savedItem);

        return RestResult.<FooItemDto>builder().code(HttpStatus.CREATED.value())
                .message("Created successfully").result(dto).build();
    }

    @Override
    @Transactional
    public RestResult<FooItemDto> updateFoo(Long id, UpdateFooItemRequest request) {
        // Find the existing entity or throw exception if not found
        FooItem fooItem = fooItemRepository.findById(id)
                .orElseThrow(() -> ResourceNotFoundException.forResource("FooItem", id));

        // Update the entity fields
        fooItem.setName(request.getName());
        fooItem.setDescription(request.getDescription());

        // Save the updated entity
        FooItem updatedItem = fooItemRepository.save(fooItem);

        // Convert to DTO and return
        FooItemDto dto = convertToDto(updatedItem);

        return RestResult.<FooItemDto>builder().code(HttpStatus.OK.value())
                .message("Updated successfully").result(dto).build();
    }

    @Override
    @Transactional
    public RestResult<Void> deleteFoo(Long id) {
        // Check if the entity exists
        if (!fooItemRepository.existsById(id)) {
            throw ResourceNotFoundException.forResource("FooItem", id);
        }

        // Delete the entity
        fooItemRepository.deleteById(id);

        // Return success response with no content
        return RestResult.<Void>builder().code(HttpStatus.OK.value())
                .message("Deleted successfully").build();
    }

    /**
     * Convert FooItem entity to FooItemDto
     * 
     * @param entity the entity to convert
     * @return the converted DTO
     */
    private FooItemDto convertToDto(FooItem entity) {
        FooItemDto dto = new FooItemDto();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        return dto;
    }
}
