package com.hillstone.hillos.demo.server;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * Foo 服务的主应用类
 * 启动 Spring Boot 应用的入口
 */
@SpringBootApplication
@EntityScan(basePackages = "com.hillstone.hillos.demo.framework.domain.entity")
@EnableJpaRepositories(basePackages = "com.hillstone.hillos.demo.framework.repository")
public class FooApplication {
    
    /**
     * 启动应用的主方法
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        SpringApplication.run(FooApplication.class, args);
    }
}
