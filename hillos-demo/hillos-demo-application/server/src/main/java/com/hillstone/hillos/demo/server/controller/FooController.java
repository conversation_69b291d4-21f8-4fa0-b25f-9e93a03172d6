package com.hillstone.hillos.demo.server.controller;

import java.util.List;

import org.springframework.web.bind.annotation.RestController;

import com.hillstone.hillos.demo.iface.CreateFooItemRequest;
import com.hillstone.hillos.demo.iface.FooFacade;
import com.hillstone.hillos.demo.iface.FooItemDto;
import com.hillstone.hillos.demo.iface.UpdateFooItemRequest;
import com.hillstone.hillos.demo.server.service.FooService;
import com.hillstone.hillos.rest.request.Query;
import com.hillstone.hillos.rest.response.RestQueryResult;
import com.hillstone.hillos.rest.response.RestResult;

/**
 * Foo 条目管理控制器 提供 Foo 条目的增删改查接口
 */
@RestController
public class FooController implements FooFacade {

    private final FooService fooService;

    public FooController(FooService fooService) {
        this.fooService = fooService;
    }

    @Override
    public RestQueryResult<List<FooItemDto>> getAllFoos(Query query) {
        return fooService.getAllFoos(query);
    }

    @Override
    public RestResult<FooItemDto> getFooById(Long id) {
        return fooService.getFooById(id);
    }

    @Override
    public RestResult<FooItemDto> createFoo(CreateFooItemRequest request) {
        return fooService.createFoo(request);
    }

    @Override
    public RestResult<FooItemDto> updateFoo(Long id, UpdateFooItemRequest request) {
        return fooService.updateFoo(id, request);
    }

    @Override
    public RestResult<Void> deleteFoo(Long id) {
        return fooService.deleteFoo(id);
    }
}
