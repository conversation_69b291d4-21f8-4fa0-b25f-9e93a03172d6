package com.hillstone.hillos.demo.server.service;

import java.util.List;

import com.hillstone.hillos.demo.iface.CreateFooItemRequest;
import com.hillstone.hillos.demo.iface.FooItemDto;
import com.hillstone.hillos.demo.iface.UpdateFooItemRequest;
import com.hillstone.hillos.rest.request.Query;
import com.hillstone.hillos.rest.response.RestQueryResult;
import com.hillstone.hillos.rest.response.RestResult;

/**
 * Service interface for managing Foo items
 */
public interface FooService {

    /**
     * Get all Foo items, with pagination and filtering support
     *
     * @param query pagination and filtering parameters
     * @return paginated list of Foo items
     */
    RestQueryResult<List<FooItemDto>> getAllFoos(Query query);

    /**
     * Get a specific Foo item by ID
     *
     * @param id the ID of the Foo item to retrieve
     * @return the requested Foo item
     */
    RestResult<FooItemDto> getFooById(Long id);

    /**
     * Create a new Foo item
     *
     * @param request data for creating the new Foo item
     * @return the created Foo item
     */
    RestResult<FooItemDto> createFoo(CreateFooItemRequest request);

    /**
     * Update an existing Foo item
     *
     * @param id the ID of the Foo item to update
     * @param request data for updating the Foo item
     * @return the updated Foo item
     */
    RestResult<FooItemDto> updateFoo(Long id, UpdateFooItemRequest request);

    /**
     * Delete a Foo item
     *
     * @param id the ID of the Foo item to delete
     * @return empty response on success
     */
    RestResult<Void> deleteFoo(Long id);
}
