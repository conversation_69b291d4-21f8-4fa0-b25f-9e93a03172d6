# Production specific configurations for hillos-demo-server
server:
  port: 8080 # Ensure this aligns with production deployment needs

spring:
  # Production datasource (e.g., MySQL)
  datasource:
    url: ******************************************************************************************************
    username: prod_db_user # Consider using environment variables: ${DB_USER}
    password: prod_db_password # Consider using environment variables: ${DB_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
    # HikariCP production settings
    hikari:
      connectionTimeout: 30000
      idleTimeout: 600000
      maxLifetime: 1800000
      maximumPoolSize: 20
      minimumIdle: 5

  jpa:
    hibernate:
      ddl-auto: validate # Schema changes should be managed by Flyway
    show-sql: false
    properties:
      hibernate.dialect: org.hibernate.dialect.MySQL8Dialect

logging:
  level:
    root: INFO
    com.hillstone.hillos.demo: INFO
    org.springframework.web: WARN
    org.hibernate: WARN

# Hillos specific configurations for production
hillstone:
  demo:
    example-property: "${HOS_DEMO_EXAMPLE_PROPERTY:production default value}"

management:
  endpoints:
    web:
      exposure:
        # Expose only necessary endpoints for production monitoring
        include: "health,info,prometheus"
  endpoint:
    health:
      show-details: when-authorized
