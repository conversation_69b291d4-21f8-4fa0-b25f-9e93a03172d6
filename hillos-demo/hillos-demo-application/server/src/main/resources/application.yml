server:
  port: 8080

spring:
  application:
    name: hillos-demo-server
  # Default to H2 in-memory database for quick start in dev profile
  datasource:
    url: jdbc:h2:mem:demodb;DB_CLOSE_DELAY=-1;MODE=MySQL;NON_KEYWORDS=USER
    username: sa
    password: sa
    driver-class-name: org.h2.Driver
  # H2 Console Configuration
  h2:
    console:
      enabled: true
      path: /h2-console
  jpa:
    hibernate:
      ddl-auto: update # Using 'update' since Flyway is disabled
    show-sql: true # Useful for development, disable in production
    properties:
      hibernate.dialect: org.hibernate.dialect.H2Dialect

  # Flyway configuration for database migrations
  flyway:
    enabled: false
    baseline-on-migrate: true
    locations: classpath:db/migration
  # Example for a real MySQL datasource (to be uncommented and configured in specific profiles)
  # datasource-mysql:
  #   url: *******************************************************************************************
  #   username: your_username
  #   password: your_password
  #   driver-class-name: com.mysql.cj.jdbc.Driver

management:
  endpoints:
    web:
      exposure:
        include: "health,info,metrics,prometheus"
  endpoint:
    health:
      show-details: always

logging:
  level:
    root: INFO
    com.hillstone.hillos.demo: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql: TRACE

# Hillos specific configurations section
hillstone:
  demo:
    # Example property with environment variable override
    example-property: "${HOS_DEMO_EXAMPLE_PROPERTY:default value}"
