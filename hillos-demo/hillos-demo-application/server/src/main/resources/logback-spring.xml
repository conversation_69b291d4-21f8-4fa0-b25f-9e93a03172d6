<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="20 seconds" debug="false">
  <!-- 日志模块名称 -->
  <property name="HOS_MODULE_NAME" value="hillos-demo"/>
  <!-- 日志保存路径 -->
  <property name="LOG_PATH" value="/data/logs/${HOS_MODULE_NAME}"/>
  <!-- 日志上下文名称 -->
  <contextName>${HOS_MODULE_NAME}</contextName>
  <!-- 日志输出模板 -->
  <include resource="logging/logback-appender.xml"/>
  <!-- 日志调试： 调整特定包日志打印级别 -->
  <include resource="logback-debug.xml"/>
  <root>
    <level value="INFO"/>
    <appender-ref ref="STDOUT"/>
    <appender-ref ref="DEBUG_FILE"/>
    <appender-ref ref="INFO_FILE"/>
    <appender-ref ref="WARN_FILE"/>
    <appender-ref ref="ERROR_FILE"/>
  </root>
</configuration>
