# hillos-demo

A Hillos application module: hillos-demo

本模块遵循 Hillos 开发规范。详情请参阅 [`CHECKLIST.md`](CHECKLIST.md)。

## 项目结构

- hillos-demo/
  - interface/  <!-- 目录名为 interface -->
    - src/main/java/com/hillstone/hillos/demo/iface/ <!-- Java 包路径为 iface -->
      - FooItemDto.java
      - CreateFooItemRequest.java
      - UpdateFooItemRequest.java
      - FooFacade.java
    - pom.xml
  - framework/
    - src/main/java/com/hillstone/hillos/demo/framework/
      - domain/entity/FooItem.java
      - repository/FooItemRepository.java
      - exception/ResourceNotFoundException.java
    - pom.xml
  - server/
    - src/main/java/com/hillstone/hillos/demo/server/
      - FooApplication.java
      - controller/FooController.java
      - service/FooService.java
      - service/impl/FooServiceImpl.java
    - src/main/resources/
      - application.yml
      - application-prod.yml
      - logback-spring.xml
      - logback-prod.xml
      - logback-debug.xml
      - i18n/demo-messages.properties
    - src/test/java/com/hillstone/hillos/demo/server/
      - controller/FooControllerTest.java
      - service/impl/FooServiceImplTest.java
    - pom.xml
  - pom.xml
  - README.md
  - CHECKLIST.md
  - lombok.config
  - sonar-project.properties
  - docs/
    - API_USAGE.md
    - ARCHITECTURE.md
    - DEPLOYMENT.md
  - server/src/main/resources/db/migration/
    - V1__init_schema.sql

## 核心功能

本项目是一个基于 Spring Boot 的应用模块，提供了一个关于 `Foo` 实体的 RESTful CRUD API 示例。

## 如何运行

1. 确保您已安装 Java (JDK 1.8+) 和 Maven (3.6+)。
2. 在项目根目录 `hillos-demo/` 下执行 `mvn clean install`。
3. 启动应用：`java -jar server/target/hillos-demo-server-2.0.0-SNAPSHOT.jar`。

## API 端点示例

API 基础路径: `/api/v1/foos`

以下是一些示例端点 (定义于 `FooFacade.java`)：

- `GET /api/v1/foos`：获取所有 Foo 列表，支持分页和过滤
- `POST /api/v1/foos`：创建一个新的 Foo
- `GET /api/v1/foos/{id}`：根据 ID 获取单个 Foo
- `PUT /api/v1/foos/{id}`：根据 ID 更新一个 Foo
- `DELETE /api/v1/foos/{id}`：根据 ID 删除一个 Foo

### cURL 示例

获取所有 Foos:
```bash
curl -X GET http://localhost:8080/api/v1/foos
```

创建一个新的 Foo:
```bash
curl -X POST -H "Content-Type: application/json" -d '{"name":"Sample Foo", "description":"A sample description"}' http://localhost:8080/api/v1/foos
```

## API 文档

详细的 API 文档可以通过 Swagger UI 查看，通常在应用启动后访问 `http://localhost:8080/swagger-ui.html`。

## 构建说明

**前提条件**:
- JDK 1.8+
- Maven 3.6+

**构建命令**:
```bash
mvn clean install
```

## 相关文档

详细文档位于 `docs` 目录：

- [API 使用指南](docs/API_USAGE.md) - 详细的 API 使用说明和示例
- [架构设计](docs/ARCHITECTURE.md) - 应用架构的详细说明
- [部署指南](docs/DEPLOYMENT.md) - 开发和生产环境的部署指南

## 数据库管理

本项目使用 Flyway 进行数据库版本控制和迁移。初始迁移脚本位于：
`server/src/main/resources/db/migration/V1__init_schema.sql`

### H2 控制台

在开发环境中，可以通过以下 URL 访问 H2 数据库控制台：
`http://localhost:8080/h2-console`

连接设置：
- JDBC URL: `jdbc:h2:mem:demodb`
- 用户名: `sa`
- 密码: `sa`

## 异常处理

本项目实现了全局异常处理机制，确保 API 返回一致的错误响应格式：

- 资源未找到（404）：当请求不存在的资源时，返回标准格式的 404 错误响应
- 请求参数错误（400）：当请求参数不符合要求时，返回标准格式的 400 错误响应
- 服务器错误（500）：当发生内部服务器错误时，返回标准格式的 500 错误响应

所有错误响应都使用统一的 `RestResult` 格式，包含错误代码、错误消息和空结果。详细信息请参阅 [API 使用指南](docs/API_USAGE.md)。
