package com.hillstone.hillos.demo.framework.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.hillstone.hillos.demo.framework.domain.entity.FooItem;

/**
 * FooItem 实体的持久化仓库接口
 * 提供对 FooItem 的基本 CRUD 操作及自定义查询扩展
 */
@Repository
public interface FooItemRepository extends JpaRepository<FooItem, Long> {
        // Spring Data JPA 会自动实现基本的 CRUD 操作
        // 如需扩展，可添加自定义查询方法
}
