package com.hillstone.hillos.demo.framework.domain.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

/**
 * Foo 条目在数据库中的实体类
 */
@Entity
@Table(name = "t_demo_foo_item")
@Data
public class FooItem {

    /**
     * Foo 条目的唯一标识
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * Foo 条目的名称
     */
    @Column(nullable = false, length = 100)
    private String name;

    /**
     * Foo 条目的描述信息
     */
    @Column(length = 500)
    private String description;
}
