package com.hillstone.hillos.demo.framework.exception;

/**
 * 当请求的资源未找到时抛出的异常
 * 用于在服务层或控制层中标识资源不存在的情况
 */
public class ResourceNotFoundException extends RuntimeException {
    
    /**
     * 使用指定的详细信息构造资源未找到异常
     *
     * @param message 详细信息
     */
    public ResourceNotFoundException(String message) {
        super(message);
    }
    
    /**
     * 为指定类型和ID的资源构造资源未找到异常
     *
     * @param resourceType 未找到的资源类型
     * @param resourceId 未找到的资源ID
     * @return 格式化消息的 ResourceNotFoundException
     */
    public static ResourceNotFoundException forResource(String resourceType, Object resourceId) {
        return new ResourceNotFoundException(
                String.format("%s with id %s not found", resourceType, resourceId));
    }
}
