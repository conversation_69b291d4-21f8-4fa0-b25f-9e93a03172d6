package com.hillstone.hillos.demo.iface;

import java.util.List;

import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.hillstone.hillos.rest.request.Query;
import com.hillstone.hillos.rest.response.RestQueryResult;
import com.hillstone.hillos.rest.response.RestResult;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "Foo 管理", description = "用于管理 Foo 条目的 API 接口")
@RequestMapping("/api/v1/foos")
@Validated
public interface FooFacade {

    @Operation(summary = "获取所有 Foo 条目", description = "检索所有 Foo 条目的列表，支持分页和过滤（通过 Query 对象）。")
    @GetMapping
    RestQueryResult<List<FooItemDto>> getAllFoos(@Parameter(description="用于过滤和分页的查询参数") Query query);

    @Operation(summary = "根据ID获取 Foo 条目", description = "根据ID检索指定的 Foo 条目。")
    @GetMapping("/{id}")
    RestResult<FooItemDto> getFooById(@Parameter(name = "id", description = "要检索的 Foo 条目的ID", required = true) @PathVariable("id") Long id);

    @Operation(summary = "创建新的 Foo 条目", description = "创建一个新的 Foo 条目。")
    @PostMapping
    RestResult<FooItemDto> createFoo(@Parameter(description = "要创建的 Foo 条目数据", required = true) @Valid @RequestBody CreateFooItemRequest request);

    @Operation(summary = "更新已有的 Foo 条目", description = "根据ID更新已有的 Foo 条目。")
    @PutMapping("/{id}")
    RestResult<FooItemDto> updateFoo(
            @Parameter(name = "id", description = "要更新的 Foo 条目的ID", required = true) @PathVariable("id") Long id,
            @Parameter(description = "更新后的 Foo 条目数据", required = true) @Valid @RequestBody UpdateFooItemRequest request);

    @Operation(summary = "根据ID删除 Foo 条目", description = "根据ID删除指定的 Foo 条目。")
    @DeleteMapping("/{id}")
    RestResult<Void> deleteFoo(@Parameter(name = "id", description = "要删除的 Foo 条目的ID", required = true) @PathVariable("id") Long id);
}
