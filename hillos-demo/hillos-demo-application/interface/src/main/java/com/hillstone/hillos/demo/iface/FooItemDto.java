package com.hillstone.hillos.demo.iface;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import lombok.Data;

/**
 * Foo 条目数据传输对象
 */
@Data
public class FooItemDto {
    
    /**
     * Foo 条目的唯一标识
     */
    private Long id;
    
    /**
     * Foo 条目的名称
     * 必填，且长度不能超过100字符
     */
    @NotBlank(message = "Name is required")
    @Size(max = 100, message = "Name must be less than 100 characters")
    private String name;
    
    /**
     * Foo 条目的描述
     * 可为空，若填写则长度不能超过500字符
     */
    @Size(max = 500, message = "Description must be less than 500 characters")
    private String description;
}
