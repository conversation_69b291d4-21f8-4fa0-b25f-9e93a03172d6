package com.hillstone.hillos.demo.iface;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import lombok.Data;

/**
 * 更新已有 Foo 条目的请求载体
 */
@Data
public class UpdateFooItemRequest {
    
    /**
     * 更新后的 Foo 条目名称
     * 必填，且长度不能超过100字符
     *
     * 安全提示：输入需净化，防止XSS攻击
     */
    @NotBlank(message = "Name is required")
    @Size(max = 100, message = "Name must be less than 100 characters")
    private String name;
    
    /**
     * 更新后的 Foo 条目描述
     * 可为空，若填写则长度不能超过500字符
     *
     * 安全提示：输入需净化，防止XSS攻击
     */
    @Size(max = 500, message = "Description must be less than 500 characters")
    private String description;
}
