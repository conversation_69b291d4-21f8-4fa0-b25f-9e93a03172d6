package com.hillstone.hillos.demo.iface;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import lombok.Data;

/**
 * 创建新 Foo 条目的请求载体
 *
 * 注意：本类不包含 'id' 字段，id 由系统自动生成
 */
@Data
public class CreateFooItemRequest {
    
    /**
     * 新 Foo 条目的名称
     * 必填，且长度不能超过100字符
     *
     * 安全提示：输入需净化，防止XSS攻击
     */
    @NotBlank(message = "Name is required")
    @Size(max = 100, message = "Name must be less than 100 characters")
    private String name;
    
    /**
     * 新 Foo 条目的描述
     * 可为空，若填写则长度不能超过500字符
     *
     * 安全提示：输入需净化，防止XSS攻击
     */
    @Size(max = 500, message = "Description must be less than 500 characters")
    private String description;
}
