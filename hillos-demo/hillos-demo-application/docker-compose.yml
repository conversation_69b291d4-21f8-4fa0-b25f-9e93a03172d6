version: "3.8"

services:
  mysql:
    image: mysql:8.0
    container_name: hillos-demo-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: demo_db
      MYSQL_USER: demo_user
      MYSQL_PASSWORD: demo_password
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    healthcheck:
      test:
        [
          "CMD",
          "mysqladmin",
          "ping",
          "-h",
          "localhost",
          "-u",
          "root",
          "-p$$MYSQL_ROOT_PASSWORD",
        ]
      interval: 5s
      timeout: 5s
      retries: 5

  app:
    build: .
    container_name: hillos-demo-app
    depends_on:
      mysql:
        condition: service_healthy
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_DATASOURCE_URL=***************************************************************************************
      - SPRING_DATASOURCE_USERNAME=demo_user
      - SPRING_DATASOURCE_PASSWORD=demo_password
    restart: always

volumes:
  mysql-data:
