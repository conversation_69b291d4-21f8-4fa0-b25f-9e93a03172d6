# HillOS模块项目规范验收清单

> **使用说明**: 本清单旨在评估HillOS模块项目是否符合《HillOS技术规范 1.0》的核心要求。可由开发人员人工自查，或在AI编程助手辅助下进行。每个检查项的"要点"列明确了核心规范，文件末尾的"AI辅助验收指南"为AI提供了操作提示。

---

## 📋 HillOS模块项目验收表

| 分类     | 评估项                                                          | 要点                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | 评估结果 | 评估备注 | 验收节点 | 验收结果 | 验收备注 |
| -------- | --------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | -------- | -------- | -------- | -------- | -------- |
| 基本准则 | 项目是否符合《HillOS技术规范 1.0》第2章基本准则                 | **核心要求**: 项目基础设施（如数据库连接、服务地址、凭证等）需支持外部化配置，允许通过环境变量（ENV）、服务配置文件（Profile）或Kubernetes ConfigMap等方式进行设置，避免在代码中硬编码具体值。                                                                                                                                                                                                                                                                                                                                                                                                   |          |          | FD评审   |          |          |
| 工程规范 | 项目是否符合《HillOS技术规范 1.0》3.2.1章节中项目命名规范要求   | **核心要求**: 项目在各平台（Gitlab、Sonar、YAPI等）及`pom.xml`中的名称需统一，并遵循"全小写，2-3单词，连字符连接"的格式 (例如: `device-core`)。                                                                                                                                                                                                                                                                                                                                                                                                                                                  |          |          | 验收评审 |          |          |
|          | 项目是否符合《HillOS技术规范 1.0》3.2.2章节中项目版本规范要求   | **核心要求**: 新模块项目的初始版本号应为 `2.0.0`。版本号格式需遵循 `<主版本>.<次版本>.<补丁版本>-<版本类型>` (例如 `2.0.0-SNAPSHOT`)。项目应使用 `${revision}` 变量进行版本管理，并在 `pom.xml` 的 `<properties>` 中定义 `revision.version` 和 `revision.modifier` 属性。                                                                                                                                                                                                                                                                                                                        |          |          | 验收评审 |          |          |
|          | 项目是否符合《HillOS技术规范 1.0》3.2.3章节中包空间规范要求     | **核心要求**: Java项目的根包（package）结构需统一为 `com.hillstone.hillos.<项目简称>`。其中 `<项目简称>` 通常源自项目的 `artifactId` (去除 `hillos-` 前缀后的小写形式)。如果 `artifactId` (去除前缀后) 包含多个词汇（例如 `device-core`），`<项目简称>` 可以是这些词汇的合并形式 (如 `devicecore`)、选择其中一个核心词汇 (如 `device` 或 `core`)、或项目自定义的其他能清晰标识项目的方式 (需在项目文档中说明其推导规则)。在此根包 (`com.hillstone.hillos.<项目简称>`) 之下，其后的所有子包结构 (可理解为 `com.hillstone.hillos.<项目简称>.*`) 均应清晰反映模块功能，并遵循项目内部的模块化设计。 |          |          | 代码评审 |          |          |
|          |                                                                 | **核心要求**: Maven项目的 `groupId` 必须为 `com.hillstone.hillos`。标准模块的 `artifactId` 应为 `hillos-<项目名>` (例如 `hillos-device-core`)；Starter模块的 `artifactId` 应为 `hillos-starter-<项目名>` 或 `<项目名>-starter`。                                                                                                                                                                                                                                                                                                                                                                 |          |          | 代码评审 |          |          |
|          | 项目是否符合《HillOS技术规范1.0》3.2.4章节中项目结构规范        | **核心要求**: 项目目录结构应体现模块化、层次化和关注点分离。必须包含 `interface/` 模块（定义REST接口契约、DTOs、Facade接口）。推荐包含 `framework/` 模块（提供通用基础框架、共享实体、数据访问基础设施等）。核心服务模块（如 `server/`）内部应包含标准分层目录，如 `config/`, `controller/`, `service/` (及其 `impl/`), `repository/`, `domain/` (或 `entity/`)。                                                                                                                                                                                                                                |          |          | 代码评审 |          |          |
|          | 项目是否符合《HillOS技术规范 1.0》3.2.5章节中项目文档规范       | **核心要求**: 项目必须提供符合规范的功能设计文档（FD）。项目根目录必须包含 `README.md` 文件（清晰说明项目概述、构建步骤、部署方法和基本使用指南）和 `CHECKLIST.md` 文件（本规范验收清单的副本或链接）。                                                                                                                                                                                                                                                                                                                                                                                          |          |          | FD评审   |          |          |
|          |                                                                 | **核心要求**: 详细的用户文档（如用户指南、操作手册）应在GitLab Wiki中进行管理。API接口文档推荐使用Swagger/OpenAPI工具自动生成，并可选择性托管到在线平台。项目根目录应包含 `docs/` 目录，用于存放或链接到这些用户文档资源。                                                                                                                                                                                                                                                                                                                                                                       |          |          | 验收评审 |          |          |
|          | 项目是否符合《HillOS技术规范 1.0》3.2.6章节中项目交付规范       | **核心要求**: 项目需按照HillOS交付约定提供规范的交付物，常见的如Maven制品（JARs, WARs, POMs）、Yum安装包、Docker镜像或ISO镜像。                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |          |          | 验收评审 |          |          |
|          |                                                                 | **核心要求**: 项目在不同开发阶段需能提供相应版本类型的交付物，例如：功能开发阶段对应功能版（FR-SNAPSHOT），持续集成阶段对应集成版（SNAPSHOT），提测阶段对应转测版（RC-SNAPSHOT），正式发布阶段对应正式版（RELEASE）。                                                                                                                                                                                                                                                                                                                                                                            |          |          | 验收评审 |          |          |
|          | 项目是否符合《HillOS技术规范 1.0》3.2.7章节中代码规范           | **核心要求**: 项目代码必须通过SonarQube静态代码扫描，且结果需符合质量门禁要求：0个Blocker/Critical级别Bug，0个漏洞，0个严重代码异味。其他代码异味应进行评审，合理部分可标记为接受或关闭规则，不合理部分需修复。代码编写需严格遵循《阿里巴巴Java开发手册》的规范。                                                                                                                                                                                                                                                                                                                                |          |          | 验收评审 |          |          |
|          | 项目是否符合《HillOS技术规范 1.0》3.2.8章节中项目流程规范       | **核心要求**: 项目的开发、测试、发布等流程需遵循HillOS体系定义的组织和管理流程规范，关键环节（如需求评审、FD评审、代码评审、验收评审）必须执行。                                                                                                                                                                                                                                                                                                                                                                                                                                                 |          |          | 验收评审 |          |          |
|          |                                                                 | **核心要求**: 项目的Git分支管理策略需符合HillOS规范，通常包括：`feature/<FR单号>` (功能开发分支), `develop` (日常集成分支), `release/<版本号>` (预发布分支), `master` (主发布分支), `hotfix/<缺陷单号>` (热修复分支) 等分支类型及其协作流程。                                                                                                                                                                                                                                                                                                                                                    |          |          | 验收评审 |          |          |
|          |                                                                 | **核心要求**: 项目的版本构建和发布过程需能接入HillOS的CICD（持续集成/持续交付）平台，并支持自动化构建和发布不同类型的版本（FR-SNAPSHOT, SNAPSHOT, RC-SNAPSHOT, RELEASE）。                                                                                                                                                                                                                                                                                                                                                                                                                       |          |          | 验收评审 |          |          |
| 框架规范 | 项目是否符合《HillOS技术规范 1.0》3.3.1章节中运行环境规范要求   | **核心要求**: Java项目默认必须采用JDK 8作为编译和运行环境。项目构建配置（如 `pom.xml`）中，Java版本属性（如 `<java.version>`）应设为 `1.8`，且Maven编译器插件的 `source` 和 `target` 参数也应配置为 `1.8` (或引用该版本属性)。项目应能兼容并在JDK 11环境下编译通过。                                                                                                                                                                                                                                                                                                                             |          |          | 验收评审 |          |          |
|          | 项目是否符合《HillOS技术规范 1.0》3.3.2章节中中间件规范要求     | **核心要求**: 项目中使用的各类中间件客户端版本需符合HillOS推荐规范。例如：数据库连接池优先使用HakariCP (版本5.7.10+)或Druid (版本21.6.5.37+，尤其ClickHouse场景)；Redis客户端优先使用Lettuce (版本5.0.6+)或Redisson (版本3.17.5+，用于分布式锁等)；REST接口调用客户端使用OpenFeign (版本2.2.10.RELEASE+)；消息中间件客户端支持Kafka (版本3.4.0+)或RabbitMQ (版本3.10.0+)。版本通常由父POM `hillos-dependencies`统一管理。                                                                                                                                                                        |          |          | 代码评审 |          |          |
|          | 项目是否符合《HillOS技术规范 1.0》3.3.3章节中服务层框架规范要求 | **核心要求**: Java项目必须统一采用Spring Boot框架作为基础服务层框架，且Spring Boot的版本必须固定为 `2.7.9` (通常由父POM `hillos-dependencies`保证)。                                                                                                                                                                                                                                                                                                                                                                                                                                             |          |          | 代码评审 |          |          |
|          |                                                                 | **核心要求**: 应用的Profile配置文件（如 `application.yml`）中，自定义的配置项命名需遵循层级结构：`hillstone.<模块名>.<配置对象>.<属性对象>.<具体属性>`。配置值必须支持通过环境变量进行覆盖，并可提供默认值，格式如：`${HOS_MYSQL_SERVER:localhost}`。                                                                                                                                                                                                                                                                                                                                            |          |          | 代码评审 |          |          |
|          |                                                                 | **核心要求**: 如果在代码或脚本中直接引用系统环境变量，其命名需遵循统一规范：`<命名空间大写>_<模块名大写>_<实体名大写>_<属性名大写>` (例如：`HOS_MONITOR_CACHE_MODE`)。                                                                                                                                                                                                                                                                                                                                                                                                                           |          |          | 代码评审 |          |          |
|          | 项目是否符合《HillOS技术规范 1.0》3.3.4章节中第三方类库规范     | **核心要求**: 项目的根 `pom.xml` 文件必须将 `com.hillstone.hillos:hillos-dependencies:2.1.0-RELEASE` POM作为其直接或间接的 `<parent>`，以实现对第三方类库版本的统一管理和约束。                                                                                                                                                                                                                                                                                                                                                                                                                  |          |          | 代码评审 |          |          |
| 接口规范 | 项目是否符合《HillOS技术规范 1.0》3.4.1章节中REST接口规范要求   | **核心要求**: 项目对外提供的RESTful API在设计与实现上，必须参考并严格遵循《HillOS REST接口规范》文档。必须依赖并使用 `com.hillstone.hillos:hillos-rest` 组件来构建标准化的API响应（如 `RestResult`, `RestQueryResult`）。Controller层方法应清晰实现定义在Facade接口中的契约，并对请求参数进行有效性校验（如使用`@Valid`）。                                                                                                                                                                                                                                                                      |          |          | 代码评审 |          |          |
|          | 项目是否符合《HillOS技术规范 1.0》3.4.2章节中消息总线设计要求   | **核心要求**: 若项目使用消息总线，消息的生产与消费必须依赖并使用 `com.hillstone.hillos:hillos-starter-message` 组件。消息主题（Topic）的命名必须遵循层级格式：`<namespace>.<application_or_module>.<event_type_or_dataset>.[<specific_event_or_data>]`，名称由小写字母和数字构成，层级间以点号`.`分隔，禁止使用下划线 `_`。消息体（通常为JSON格式）必须包含 `id` (String), `timestamp` (long,毫秒级Unix时间戳), `msgType` (String)三个字段。                                                                                                                                                     |          |          | FD评审   |          |          |
|          | 项目是否符合《HillOS技术规范 1.0》3.4.3章节中南向协议约定要求   | **核心要求**: 若项目涉及与特定硬件设备或第三方系统通过非标准协议（南向协议）进行通信，其协议实现必须遵循对应已定义的HillOS南向协议规范文档（如有）。若无此类集成，则本项不适用。                                                                                                                                                                                                                                                                                                                                                                                                                 |          |          |          |          |          |
| 设施约定 | 项目是否符合《HillOS技术规范 1.0》3.5.1章节中操作系统约定       | **核心要求**: 应用模块需设计为能在HillOS体系当前推荐的操作系统版本上运行（例如：AlmaLinux 8.8, OpenEuler 20.03 LTS-SP3或更高LTS版本）。若通过容器化部署，Dockerfile中基础镜像应选用这些推荐的OS。HillOS中台和应用层组件的默认安装根目录为 `/user/local/hillstone`。                                                                                                                                                                                                                                                                                                                              |          |          | FD评审   |          |          |
|          | 项目是否符合《HillOS技术规范 1.0》3.5.2章节中数据库约定         | **核心要求**: 数据库Schema设计需符合命名约定：库名格式 `<产品线>_<模块名>` (如 `hillos_core`)；表名格式 `t_<模块名>_<实体或关系名>` (如 `t_core_user`)；字符集默认使用 `utf8mb4`；表和字段必须有清晰注释；主键推荐统一命名为 `id`，类型为 `BIGINT AUTO_INCREMENT` (分布式ID场景除外，需在FD中说明)。索引和约束命名也需符合规范（如普通索引 `idx_<table>_<column>`，唯一索引 `uk_<table>_<column>`，外键 `fk_<table>_<column>`）。项目必须采用Flyway进行数据库Schema的版本化控制和自动化迁移。                                                                                                    |          |          | 代码评审 |          |          |
|          |                                                                 | **核心要求**: Flyway数据库迁移脚本必须遵循命名规范：版本化迁移脚本为 `V<版本号>__<英文描述>.sql` (版本号如1.0.0)，可重复执行的迁移脚本为 `R__<版本号>__<英文描述>.sql`。脚本必须存放在Maven标准资源路径下的特定目录，通常为 `<project.basedir>/src/main/resources/<project.name>/db/migration` (或 `db/migration` 若无 `<project.name>` 子目录)。可重复执行的迁移脚本（R脚本）其SQL语句必须保证幂等性。                                                                                                                                                                                          |          |          | 代码评审 |          |          |
|          | 项目是否符合《HillOS技术规范 1.0》3.5.3章节中消息中间件要求     | **核心要求**: 项目使用的消息中间件平台（如Kafka, RabbitMQ）其版本需符合HillOS体系推荐版本（例如：Kafka 3.4.0+）。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |          |          | FD评审   |          |          |
|          | 项目是否符合《HillOS技术规范 1.0》3.5.4章缓存层要求             | **核心要求**: 若使用Redis作为分布式缓存，其缓存键（key）的命名必须采用多层级结构，层级间以冒号 `:` 分隔，格式通常为 `<命名空间>:<模块名>:<实体名>:<具体键>` (各层级由大写单词或有意义简写构成，如 `HOS:MONITOR:DEVICE_STATUS:{deviceId}`)。若使用本地缓存（如Caffeine），在设计时必须充分考虑分布式环境下数据一致性问题。                                                                                                                                                                                                                                                                        |          |          |          |          |          |
| 测试规范 | 项目是否符合《HillOS技术规范 1.0》3.6.1章节中项目单元测试要求   | **核心要求**: 所有新开发的功能代码或对现有代码的任何逻辑变更，都必须同步创建或更新相应的单元测试用例。Java项目单元测试必须采用JUnit 5 (Jupiter)作为核心测试框架，配合Mockito进行测试替身（Mock/Spy）的创建与管理，推荐使用AssertJ提供流式断言。单元测试必须覆盖正常的业务流程、各种边界条件以及潜在的异常情况。                                                                                                                                                                                                                                                                                  |          |          | 验收评审 |          |          |
|          | 项目是否符合《HillOS技术规范 1.0》3.6.2章节中项目集成测试要求   | **核心要求**: HillOS模块项目代码中必须包含必要的集成测试。集成测试可以采用多种方式，如基于Mock的集成（测试服务间契约）、端对端（E2E）流程集成（模拟用户真实操作路径）、或特定产品线场景的集成。对于Spring Boot项目，推荐充分利用 `spring-boot-starter-test` 提供的支持，如使用 `@SpringBootTest` 加载应用上下文，`@MockBean` 模拟外部Bean，以及 `TestRestTemplate` 或 `MockMvc` 测试Controller层。                                                                                                                                                                                               |          |          |          |          |          |
|          | 项目是否符合《HillOS技术规范 1.0》3.6.3章节中代码覆盖率要求     | **核心要求**: 项目的自动化测试（尤其是单元测试）所达到的代码行覆盖率必须不低于 `60%`。此覆盖率通过JaCoCo插件生成报告，并在SonarQube平台上进行度量和监控。                                                                                                                                                                                                                                                                                                                                                                                                                                        |          |          | 验收评审 |          |          |
| 安全规范 | 项目是否符合《HillOS技术规范》3.7章节中安全规范要求             | **核心要求**: 项目必须遵循HillOS核心安全规范：默认密码需满足复杂度要求(≥12字符，含大小写字母、数字、特殊符号)。对所有外部输入（API参数、用户表单等）进行严格校验与净化，防止XSS、命令注入等。数据库交互必须使用参数化查询（如PreparedStatement）或安全的ORM机制，严禁SQL拼接。敏感数据（如密码）存储时必须加密。对外通信强制使用TLS/SSL加密（如HTTPS）。系统访问控制遵循最小权限原则，防火墙合理配置。机密信息（密钥、token、数据库密码等）必须动态管理，严禁硬编码在代码或普通配置文件中。                                                                                                      |          |          | FD评审   |          |          |

---

## 🤖 AI辅助验收指南

本指南旨在为AI编程助手提供如何辅助完成上述检查表中各项检查的指导。AI应根据以下提示，分析项目代码和配置文件，并尝试填写主检查表中的"评估结果"和"评估备注"列，同时根据"评估结果"建议"验收结果"。

**重要：AI助手在填写'评估结果'后，请务必根据以下规则建议'验收结果'：若'评估结果'为 ✅ 符合，则'验收结果'为'通过'；若'评估结果'为 ❌ 不符合、⚠️ 需关注 或 🔍 需人工确认，则'验收结果'为'待评审'。**

**通用AI检查原则：**

* **理解意图**：仔细阅读每个"评估项"及其对应的"要点"，理解其核心规范要求。
* **利用能力**：综合运用文件读取、代码解析、文本搜索、模式匹配、甚至命令执行（如果被允许并配置安全执行环境）等能力。
* **结果映射**：
* **评估结果**：根据判断，选择填入 ✅ 符合 / ❌ 不符合 / ⚠️ 需关注 / 🔍 需人工确认 / 📋 不涉及。
* **评估备注**：清晰说明判断依据。如果是 ✅，简述关键符合点；如果是 ❌ 或 ⚠️，指出具体问题、不符合项、文件名和行号（如果适用）；如果是 🔍，说明需要人工确认的内容。

---

### 1. 基本准则

**评估项：项目是否符合《HillOS技术规范 1.0》第2章基本准则**

* **AI提示**：
* **💡 AI请关注：** 项目的核心配置文件（如 `application.yml`, `bootstrap.yml`），部署脚本（如 `Dockerfile`, Kubernetes `yaml`）。查找数据库连接、服务地址、用户名、密码、API密钥等基础设施配置。
* **核心判断逻辑：** 判断这些配置是否直接硬编码了具体值。规范要求使用环境变量占位符（如 `${ENV_NAME}` 或 `${ENV_NAME:defaultValue}`）、Spring Cloud Config 客户端、或Kubernetes ConfigMap/Secret引用等方式实现外部化。
* **注意：** 开发环境或测试环境的示例性硬编码（如H2内存数据库的`sa/sa`）可标记为 ⚠️ 并备注，生产相关的配置硬编码则应标记为 ❌。

### 2. 工程规范

**评估项：项目是否符合《HillOS技术规范 1.0》3.2.1章节中项目命名规范要求**

* **AI提示**：
* **💡 AI请关注：** 项目根 `pom.xml` 中的 `<artifactId>` 和 `<n>`；`sonar-project.properties` 文件中的 `sonar.projectKey` 和 `sonar.projectName`。
* **核心判断逻辑：** 检查上述提取的名称是否都符合"全小写，2-3单词，连字符连接"的格式。
* **注意：** Gitlab、YAPI等外部平台名称AI无法直接验证，请在备注中说明内部命名检查结果，并将评估结果标记为 🔍，提示人工确认外部平台命名一致性。

**评估项：项目是否符合《HillOS技术规范 1.0》3.2.2章节中项目版本规范要求**

* **AI提示**：
* **💡 AI请关注：** 项目根 `pom.xml` 文件。
* **核心判断逻辑：** 检查顶层 `<version>` 标签内容是否为 `${revision}`。检查 `<properties>` 部分是否存在 `<revision.version>`（新项目初始值应为 `2.0.0`）和 `<revision.modifier>`（如 `SNAPSHOT`）。
* **注意：** 版本格式 `<主>.<次>.<补丁>-<版本类型>` 是通过这三个元素组合而成。

**评估项：项目是否符合《HillOS技术规范 1.0》3.2.3章节中包空间规范要求 (第一个要点：Java包结构)**

* **AI提示**：
* **💡 AI请关注：** `src/main/java` 目录下所有 `.java` 文件的 `package` 声明。项目根 `pom.xml` 的 `<artifactId>`。
* **核心判断逻辑：** 从 `<artifactId>` (例如 `hillos-device-core`) 推断项目简称 (例如 `device.core` 或 `devicecore`，需根据实际项目约定调整推断逻辑，通常是去除 `hillos-` 并将连字符转为点或合并)。验证所有 `package` 声明是否以 `com.hillstone.hillos.<推断出的项目简称>` 为前缀。
* **注意：** 如果项目简称推断复杂，可提示 🔍。

**评估项：(第二个要点：Maven groupId 和 artifactId)**

* **AI提示**：
* **💡 AI请关注：** 项目根 `pom.xml` 文件。
* **核心判断逻辑：** 验证顶层（或通过 `<parent>` 继承的）`<groupId>` 是否为 `com.hillstone.hillos`。验证顶层 `<artifactId>` 是否以 `hillos-` 开头（标准模块）或符合 `hillos-starter-xxx` / `xxx-starter` （Starter模块）的命名规范。

**评估项：项目是否符合《HillOS技术规范1.0》3.2.4章节中项目结构规范**

* **AI提示**：
* **💡 AI请关注：** 项目根目录及各主要模块（如 `interface`, `framework`, `server`）的目录结构。
* **核心判断逻辑：** 使用文件列表功能检查。验证根目录是否存在 `interface/` 模块目录。若存在 `server/` (或类似核心业务模块)，检查其 `src/main/java/.../<项目简称>/` 下是否存在 `controller/`, `service/` (及 `impl/`), `repository/`, `domain/` (或 `entity/`) 等标准分层子目录。若存在 `framework/` 模块，检查其下是否有 `domain/entity/`, `repository/` 等。
* **注意：** `config/` 目录可能不是必需的，Spring Boot的配置类可以放在主包下的 `config` 子包或直接在启动类中配置。

**评估项：项目是否符合《HillOS技术规范 1.0》3.2.5章节中项目文档规范 (第一个要点：FD, README, CHECKLIST)**

* **AI提示**：
* **💡 AI请关注：** 项目根目录下的文件列表。
* **核心判断逻辑：** 检查是否存在 `README.md` 和 `CHECKLIST.md` 文件。可对 `README.md` 内容进行关键词分析（如查找"项目概述"、"构建"、"部署"等标题）判断其是否包含基本信息。
* **注意：** FD设计文档的规范性和存在性AI难以直接验证，请将评估结果标记为 🔍，并在备注中说明 `README.md` 和 `CHECKLIST.md` 的检查情况，提示人工确认FD文档。

**评估项：(第二个要点：用户文档 - Wiki, API文档, docs/目录)**

* **AI提示**：
* **💡 AI请关注：** 项目根目录文件列表，`pom.xml` 依赖，Java源代码中的注解。
* **核心判断逻辑：** 检查根目录是否存在 `docs/` 目录。扫描 `pom.xml` (特别是 `server` 模块) 是否包含 `springdoc-openapi-ui` 或类似Swagger/OpenAPI的依赖。扫描Java代码（特别是Facade接口）是否存在 `@Tag`, `@Operation` 等API文档注解。
* **注意：** GitLab Wiki内容AI无法验证。如果支持API文档生成但缺少 `docs/` 目录，可标记为 ⚠️。

**评估项：项目是否符合《HillOS技术规范 1.0》3.2.6章节中项目交付规范 (第一个要点：交付物类型)**

* **AI提示**：
* **💡 AI请关注：** 项目各模块 `pom.xml` 中的 `<packaging>` 标签。项目根目录是否包含 `Dockerfile`。
* **核心判断逻辑：** 根据 `<packaging>` (如 `jar`, `war`, `pom`) 和 `Dockerfile` 的存在性判断主要交付物类型。
* **注意：** 如果交付目标不明确，可标记为 🔍。

**评估项：(第二个要点：交付物版本类型)**

* **AI提示**：
* **💡 AI请关注：** 项目根 `pom.xml` 中的版本管理方式，特别是 `revision.modifier` 属性。
* **核心判断逻辑：** 检查 `revision.modifier` 是否被使用，并是否能支持 `SNAPSHOT`, `RELEASE`, `RC-SNAPSHOT`, `FR-SNAPSHOT` 等不同类型的值（通常通过CICD传入覆盖）。
* **注意：** AI主要检查配置上是否支持，实际各阶段是否提供了正确类型交付物需结合CICD流程。

**评估项：项目是否符合《HillOS技术规范 1.0》3.2.7章节中代码规范 (SonarQube, 阿里巴巴手册)**

* **AI提示**：
* **💡 AI请关注：** 项目根目录的 `sonar-project.properties` 和 `lombok.config` 文件。
* **核心判断逻辑：** 检查 `sonar-project.properties` 是否存在且核心配置（`sonar.projectKey`, `sonar.projectName`, `sonar.sources`, `sonar.modules`, `sonar.java.coveragePlugin=jacoco`, `sonar.sourceEncoding=UTF-8`）是否完整。检查 `lombok.config` 是否存在并包含 `lombok.addLombokGeneratedAnnotation = true`。
* **注意：** SonarQube实际扫描结果（0 Bug, 0漏洞, 0异味）和《阿里巴巴Java开发手册》的全面遵循情况，AI无法直接验证。请将评估结果标记为 ⚠️，备注说明配置文件检查情况，并提示需通过实际扫描和代码评审确认。

**评估项：项目是否符合《HillOS技术规范 1.0》3.2.8章节中项目流程规范 (评审流程)**

* **AI提示**：
* **结果填写指导**：标记为 📋 不涉及 (AI检查范围之外) 或 🔍 需人工确认。

**评估项：(Git分支管理)**

* **AI提示**：
* **结果填写指导**：标记为 📋 不涉及 (AI检查范围之外) 或 🔍 需人工确认。

**评估项：(CICD集成)**

* **AI提示**：
* **💡 AI请关注：** 项目根 `pom.xml`。
* **核心判断逻辑：** 检查版本管理是否使用了 `${revision}` 及 `revision.version`, `revision.modifier` 属性。检查是否配置了 `flatten-maven-plugin`（通常由 `hillos-dependencies` 父POM管理，检查 `pluginManagement` 或直接使用）。
* **注意：** 这些是支持CICD的基础配置。

### 3. 框架规范

**评估项：项目是否符合《HillOS技术规范 1.0》3.3.1章节中运行环境规范要求 (JDK)**

* **AI提示**：
* **💡 AI请关注：** 项目根 `pom.xml`。
* **核心判断逻辑：** 检查 `<properties>` 中 `<java.version>` 是否明确设置为 `1.8`。检查 `maven-compiler-plugin` 的 `<configuration>` 中 `<source>` 和 `<target>` 是否都设置为 `${java.version}` (或直接为 `1.8`)。

**评估项：项目是否符合《HillOS技术规范 1.0》3.3.2章节中中间件规范要求**

* **AI提示**：
* **💡 AI请关注：** 项目所有 `pom.xml` 文件中的 `<dependencies>`。
* **核心判断逻辑：**
        1. 查找 `spring-boot-starter-jdbc` 或 `HikariCP` (数据库连接池)。
        2. 查找 `spring-boot-starter-data-redis` (Lettuce) 或 `redisson-spring-boot-starter` (Redis)。
        3. 查找 `spring-cloud-starter-openfeign` (REST调用)。
        4. 查找 `spring-kafka` 或 `spring-boot-starter-amqp` (消息)。
        5. **版本检查**：提取这些关键依赖的版本号（若显式声明）。由于版本通常由 `hillos-dependencies` 父POM统一管理，AI可能无法直接获取最终生效版本。此时，AI应检查这些依赖是否未指定版本（从而依赖父POM），或者其显式声明的版本是否与规范冲突。
* **结果填写指导**：若关键依赖存在且版本由父POM管理（或显式版本符合），则为 ✅。若发现不合规依赖或版本，则为 ❌。若未使用某类中间件，则为 📋。若版本判断需依赖父POM具体内容，可标记 ⚠️ 并说明。

**评估项：项目是否符合《HillOS技术规范 1.0》3.3.3章节中服务层框架规范要求 (SpringBoot版本)**

* **AI提示**：
* **💡 AI请关注：** 项目根 `pom.xml`。
* **核心判断逻辑：** 检查 `<parent>` 是否为 `com.hillstone.hillos:hillos-dependencies:2.1.0-RELEASE`。此父POM应保证Spring Boot版本为 `2.7.9`。AI可基于此信任链进行判断。如果项目中直接覆盖了 `spring-boot.version` 属性，则需检查其值。
* **结果填写指导**：如果父POM正确且未被覆盖为错误版本，则为 ✅。否则为 ⚠️ 或 ❌。

**评估项：(Profile配置规范)**

* **AI提示**：
* **💡 AI请关注：** `src/main/resources/application*.yml` (或 `.properties`) 文件。
* **核心判断逻辑：** 查找以 `hillstone.` 开头的自定义配置块，验证其后续层级是否符合 `hillstone.<module-name>.<config-object>.<property-object>.<property>`。检查配置值中是否存在 `${ENV_VAR:defaultValue}` 格式。

**评估项：(环境变量命名规范)**

* **AI提示**：
* **💡 AI请关注：** Java代码中对 `System.getenv("KEY_NAME")` 的调用，配置文件中对 `${KEY_NAME}` 的引用。
* **核心判断逻辑：** 对提取出的 `KEY_NAME` 进行模式匹配，看是否符合 `<NAMESPACE>_<MODULE>_<ENTITY>_<PROPERTY>` 的大写下划线格式。

**评估项：项目是否符合《HillOS技术规范 1.0》3.3.4章节中第三方类库规范 (父POM)**

* **AI提示**：
* **💡 AI请关注：** 项目根 `pom.xml`。
* **核心判断逻辑：** 验证 `<parent>` 元素是否正确配置为 `<groupId>com.hillstone.hillos</groupId>`, `<artifactId>hillos-dependencies</artifactId>`, `<version>2.1.0-RELEASE</version>`。

### 4. 接口规范

**评估项：项目是否符合《HillOS技术规范 1.0》3.4.1章节中REST接口规范要求**

* **AI提示**：
* **💡 AI请关注：** `server` 模块的 `pom.xml`，`controller` 包下的Java类，`interface` 模块中的Facade接口定义。
* **核心判断逻辑：**
        1. `pom.xml` 是否含 `com.hillstone.hillos:hillos-rest` 依赖。
        2. Controller方法是否返回 `com.hillstone.hillos.rest.response.RestResult` 或 `RestQueryResult`。
        3. Controller类是否实现对应的Facade接口。
        4. Facade接口方法参数中，`@RequestBody` 注解的对象是否使用了 `@Valid`，且DTO内部字段是否有约束注解。

**评估项：项目是否符合《HillOS技术规范 1.0》3.4.2章节中消息总线设计要求**

* **AI提示**：
* **💡 AI请关注：** `pom.xml` 依赖，代码中消息监听注解（`@KafkaListener`, `@RabbitListener`）和消息发送API调用，消息体POJO定义。
* **核心判断逻辑：**
        1. 是否依赖 `com.hillstone.hillos:hillos-starter-message`。
        2. Topic名称是否符合 `<namespace>.<app/module>.<event/dataset>.[<event/data>]` 格式。
        3. 消息体Java类是否包含 `id` (String), `timestamp` (long), `msgType` (String) 字段。
* **结果填写指导**：未使用则为 📋。

**评估项：项目是否符合《HillOS技术规范 1.0》3.4.3章节中南向协议约定要求**

* **AI提示**：
* **结果填写指导**：若项目不涉及特定南向协议，则为 📋。否则为 🔍，备注需提供具体协议规范。

### 5. 设施约定

**评估项：项目是否符合《HillOS技术规范 1.0》3.5.1章节中操作系统约定**

* **AI提示**：
* **💡 AI请关注：** 项目中的 `Dockerfile` (如果存在)。
* **核心判断逻辑：** 分析 `Dockerfile` 的 `FROM` 指令是否为推荐的OS基础镜像。检查应用安装路径是否倾向于 `/user/local/hillstone`。
* **结果填写指导**：无 `Dockerfile` 则为 🔍。

**评估项：项目是否符合《HillOS技术规范 1.0》3.5.2章节中数据库约定 (命名, Flyway)**

* **AI提示**：
* **💡 AI请关注：** JPA实体类注解 (`@Table`, `@Column`, `@Id`, `@GeneratedValue`)，`application.yml` 中的数据库连接URL，`pom.xml` Flyway依赖，`src/main/resources/db/migration` 目录。
* **核心判断逻辑：** 验证表名 (`t_<module>_<entity>`)、字段名（小写下划线）、主键 (`Long id`, `IDENTITY`)、库名 (`<product>_<module>`)。检查是否使用Flyway。

**评估项：(Flyway脚本规范)**

* **AI提示**：
* **💡 AI请关注：** `db/migration` 目录下的 `.sql` 文件名。
* **核心判断逻辑：** 验证脚本名是否符合 `V<version>__<desc>.sql` 或 `R__<version>__<desc>.sql`。
* **结果填写指导**：未使用Flyway则为 📋。R脚本幂等性需 ⚠️。

**评估项：项目是否符合《HillOS技术规范 1.0》3.5.3章节中消息中间件要求 (版本)**

* **AI提示**：
* **💡 AI请关注：** `pom.xml` 中消息中间件客户端依赖的版本。
* **核心判断逻辑：** 对比依赖版本与规范要求版本（Kafka 3.4.0+, RabbitMQ 3.10.0+）。
* **结果填写指导**：未使用则为 📋。

**评估项：项目是否符合《HillOS技术规范 1.0》3.5.4章缓存层要求 (Redis键命名)**

* **AI提示**：
* **💡 AI请关注：** 代码中操作Redis缓存的部分（`RedisTemplate`, `@Cacheable`）。
* **核心判断逻辑：** 提取使用的缓存键，验证是否符合 `<NAMESPACE>:<MODULE>:<ENTITY>:<KEY>` 格式。
* **结果填写指导**：未使用Redis则为 📋。

### 6. 测试规范

**评估项：项目是否符合《HillOS技术规范 1.0》3.6.1章节中项目单元测试要求 (框架, 内容)**

* **AI提示**：
* **💡 AI请关注：** `src/test/java` 目录下的测试类，导入语句，注解。
* **核心判断逻辑：** 检查是否使用JUnit 5, Mockito, AssertJ。抽样检查测试方法是否覆盖基本路径。
* **结果填写指导**：内容完备性需 ⚠️。

**评估项：项目是否符合《HillOS技术规范 1.0》3.6.2章节中项目集成测试要求**

* **AI提示**：
* **💡 AI请关注：** `src/test/java` 目录下的测试类。
* **核心判断逻辑：** 查找是否使用 `@SpringBootTest`, `@MockBean`, `TestRestTemplate`/`MockMvc`。
* **结果填写指导**：无集成测试迹象则为 ❌ (除非项目性质无需)。

**评估项：项目是否符合《HillOS技术规范 1.0》3.6.3章节中代码覆盖率要求**

* **AI提示**：
* **💡 AI请关注：** `pom.xml` 中 `jacoco-maven-plugin` 配置。
* **核心判断逻辑：** AI无法直接执行命令获取覆盖率。
* **结果填写指导**：若插件已配置，则为 🔍，备注提示用户执行 `mvn clean test jacoco:report` 并提供覆盖率数据。若未配置，则为 ❌。

### 7. 安全规范

**评估项：项目是否符合《HillOS技术规范》3.7章节中安全规范要求**

* **AI提示**：
* **💡 AI请关注：** 配置文件中的密码硬编码，Controller参数验证注解，Repository层SQL构造方式，SSL配置，代码中敏感信息硬编码。
* **核心判断逻辑：**
        1. 默认密码：扫描简单硬编码密码。
        2. 输入验证：Facade接口参数是否有 `@Valid`，DTO内字段是否有约束注解。
        3. SQL注入：是否使用JPA或参数化查询。
        4. 通信安全：`application.yml` 中 `server.ssl.enabled=true`。
        5. 机密信息：扫描代码和配置文件中硬编码的 `password`, `apiKey`, `secretKey`。
* **结果填写指导**：综合评估，列出所有发现的问题。

---
