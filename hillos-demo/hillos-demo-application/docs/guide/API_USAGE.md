# Hillos Demo API 使用指南

这个文档提供了 Hillos Demo API 的详细使用说明。

## API 端点

所有 API 端点都以 `/api/v1/foos` 为基础路径。

### 获取所有 Foo 项目

```
GET /api/v1/foos
```

示例请求：
```bash
curl -X GET http://localhost:8080/api/v1/foos
```

示例响应：
```json
{
  "code": 200,
  "message": "Success",
  "result": [
    {
      "id": 1,
      "name": "Test Foo",
      "description": "A test Foo item"
    }
  ],
  "total": 1
}
```

### 通过 ID 获取 Foo 项目

```
GET /api/v1/foos/{id}
```

示例请求：
```bash
curl -X GET http://localhost:8080/api/v1/foos/1
```

示例响应：
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "id": 1,
    "name": "Test Foo",
    "description": "A test Foo item"
  }
}
```

### 创建新的 Foo 项目

```
POST /api/v1/foos
```

示例请求：
```bash
curl -X POST -H "Content-Type: application/json" -d '{"name":"Test Foo","description":"A test Foo item"}' http://localhost:8080/api/v1/foos
```

示例响应：
```json
{
  "code": 201,
  "message": "Created successfully",
  "result": {
    "id": 1,
    "name": "Test Foo",
    "description": "A test Foo item"
  }
}
```

### 更新 Foo 项目

```
PUT /api/v1/foos/{id}
```

示例请求：
```bash
curl -X PUT -H "Content-Type: application/json" -d '{"name":"Updated Foo","description":"An updated Foo item"}' http://localhost:8080/api/v1/foos/1
```

示例响应：
```json
{
  "code": 200,
  "message": "Updated successfully",
  "result": {
    "id": 1,
    "name": "Updated Foo",
    "description": "An updated Foo item"
  }
}
```

### 删除 Foo 项目

```
DELETE /api/v1/foos/{id}
```

示例请求：
```bash
curl -X DELETE http://localhost:8080/api/v1/foos/1
```

示例响应：
```json
{
  "code": 200,
  "message": "Deleted successfully",
  "result": null
}
```

## 错误处理

API 使用标准 HTTP 状态码表示请求状态：

- 200: 成功
- 201: 创建成功
- 400: 请求参数错误
- 404: 资源未找到
- 500: 服务器内部错误

当错误发生时，响应将包含错误详情：

### 资源未找到错误 (404)

如果尝试访问不存在的资源，API 将返回 404 状态码和标准的 RestResult 响应格式：

```json
{
  "code": 404,
  "message": "FooItem with id 999 not found",
  "result": null
}
```

此响应适用于以下情况：
- 获取不存在的 Foo 项目 (`GET /api/v1/foos/{id}`)
- 更新不存在的 Foo 项目 (`PUT /api/v1/foos/{id}`)
- 删除不存在的 Foo 项目 (`DELETE /api/v1/foos/{id}`)
