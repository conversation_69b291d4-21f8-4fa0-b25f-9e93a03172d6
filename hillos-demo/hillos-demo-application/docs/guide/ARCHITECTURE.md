# Hillos Demo 架构设计

本文档描述了 Hillos Demo 应用的架构设计。

## 整体架构

Hillos Demo 采用标准的 Hillos 应用模块结构，包括三个主要模块：

1. **interface 模块** - 定义了与外部系统交互的接口
2. **framework 模块** - 包含领域模型和核心业务逻辑
3. **server 模块** - 实现具体的服务逻辑和控制器

## 模块依赖关系

```
server → framework → interface
```

- interface 模块不依赖于其他模块
- framework 模块依赖于 interface 模块
- server 模块依赖于 framework 和 interface 模块

## 主要组件

### 1. 实体和仓库

- `FooItem` - 代表 Foo 项目的实体类，使用 JPA 注解映射到数据库表
- `FooItemRepository` - Spring Data JPA 仓库接口，提供对 Foo 项目的 CRUD 操作

### 2. DTO 和请求对象

- `FooItemDto` - 传输对象，用于在 API 层暴露 Foo 项目数据
- `CreateFooItemRequest` - 创建 Foo 项目的请求对象
- `UpdateFooItemRequest` - 更新 Foo 项目的请求对象

### 3. 接口和实现

- `FooFacade` - REST API 接口定义，使用 OpenAPI 注解标记
- `FooController` - 实现 FooFacade 接口的控制器
- `FooService` - 服务层接口
- `FooServiceImpl` - 服务层接口的实现

## 数据流

1. 客户端通过 HTTP 请求调用 REST API
2. `FooController` 接收请求并委托给 `FooService`
3. `FooService` 实现业务逻辑，通过 `FooItemRepository` 与数据库交互
4. `FooService` 将实体数据转换为 DTO 并返回给 `FooController`
5. `FooController` 将响应返回给客户端

## 环境配置

Hillos Demo 支持多种运行环境：

- **默认/开发环境** - 使用 H2 内存数据库
- **生产环境** - 配置为使用 MySQL 数据库

环境配置文件：
- `application.yml` - 默认配置
- `application-prod.yml` - 生产环境配置

## 日志策略

Hillos Demo 使用 Logback 进行日志记录，提供了多种日志配置：

- `logback-spring.xml` - 默认日志配置
- `logback-prod.xml` - 生产环境日志配置
- `logback-debug.xml` - 调试环境日志配置

日志级别默认设置：
- root: INFO
- com.hillstone.hillos.demo: DEBUG
- org.springframework.web: INFO
- org.hibernate.SQL: DEBUG (开发环境)

## 异常处理架构

Hillos Demo 实现了一个健壮的异常处理机制，确保 API 返回一致的错误响应：

### 异常类
- `ResourceNotFoundException` - 当请求的资源不存在时抛出
- 其他标准 Spring 异常类用于处理各种错误情况

### 全局异常处理
- `GlobalExceptionHandler` - 使用 Spring 的 `@ControllerAdvice` 注解实现的全局异常处理器
  - 将 `ResourceNotFoundException` 转换为 HTTP 404 响应
  - 将其他未处理的异常转换为 HTTP 500 响应
  - 确保所有异常响应使用统一的 `RestResult` 格式

### 响应格式
所有 API 响应（包括错误）都使用标准的 `RestResult` 或 `RestQueryResult` 格式，包含：
- `code` - HTTP 状态码
- `message` - 成功或错误消息
- `result` - 响应数据（错误情况下为 null）
- `total` - 查询结果的总数（仅用于列表查询）
