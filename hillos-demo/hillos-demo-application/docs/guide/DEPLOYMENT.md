# Hillos Demo 部署指南

本文档提供了 Hillos Demo 应用的部署说明。

## 先决条件

- JDK 8 或更高版本
- Maven 3.6 或更高版本
- 对于生产环境：MySQL 5.7 或更高版本

## 开发环境部署

### 1. 构建项目

```bash
cd /path/to/hillos-demo
mvn clean install
```

### 2. 运行应用程序

```bash
cd server
java -jar target/hillos-demo-server-2.0.0-SNAPSHOT.jar
```

开发环境默认使用内存 H2 数据库，无需额外配置。应用程序将在 http://localhost:8080 上启动。

### 3. 访问 H2 控制台

开发环境中，可以通过以下 URL 访问 H2 数据库控制台：

```
http://localhost:8080/h2-console
```

连接设置：
- JDBC URL: `jdbc:h2:mem:demodb`
- 用户名: `sa`
- 密码: `sa`

### 4. 访问 API 文档

通过以下 URL 访问 Swagger UI API 文档：

```
http://localhost:8080/swagger-ui.html
```

## 生产环境部署

### 1. 准备数据库

在 MySQL 中创建数据库：

```sql
CREATE DATABASE demo_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'demo_user'@'%' IDENTIFIED BY 'your_strong_password';
GRANT ALL PRIVILEGES ON demo_db.* TO 'demo_user'@'%';
FLUSH PRIVILEGES;
```

### 2. 配置生产环境属性

编辑 `application-prod.yml` 文件中的数据库连接信息：

```yaml
spring:
  datasource:
    url: **********************************************************************************************
    username: demo_user
    password: your_strong_password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### 3. 构建项目

```bash
cd /path/to/hillos-demo
mvn clean install
```

### 4. 部署和运行

使用生产环境配置文件启动应用程序：

```bash
cd server
java -jar -Dspring.profiles.active=prod target/hillos-demo-server-2.0.0-SNAPSHOT.jar
```

### 5. 监控

应用程序提供了 Spring Boot Actuator 端点，可用于监控应用程序的健康状态和指标：

```
http://localhost:8080/actuator/health
http://localhost:8080/actuator/info
http://localhost:8080/actuator/metrics
```

## Docker 部署（可选）

### 1. 创建 Dockerfile

在项目根目录创建 Dockerfile：

```Dockerfile
FROM openjdk:8-jre-alpine

WORKDIR /app

COPY server/target/hillos-demo-server-2.0.0-SNAPSHOT.jar /app/app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-Dspring.profiles.active=prod", "-jar", "/app/app.jar"]
```

### 2. 构建 Docker 镜像

```bash
docker build -t hillos-demo:latest .
```

### 3. 运行 Docker 容器

```bash
docker run -d -p 8080:8080 \
  -e SPRING_DATASOURCE_URL=************************************************* \
  -e SPRING_DATASOURCE_USERNAME=demo_user \
  -e SPRING_DATASOURCE_PASSWORD=your_strong_password \
  --name hillos-demo \
  hillos-demo:latest
```
