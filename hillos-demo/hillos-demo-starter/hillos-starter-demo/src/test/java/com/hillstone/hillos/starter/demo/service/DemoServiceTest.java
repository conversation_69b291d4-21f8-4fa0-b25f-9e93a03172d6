package com.hillstone.hillos.starter.demo.service;

import com.hillstone.hillos.starter.demo.DemoProperties;
import com.hillstone.hillos.starter.demo.service.impl.DemoServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * DemoService 单元测试
 * 
 * <AUTHOR> AI Assistant
 * @since 2.0.0
 */
@ExtendWith(MockitoExtension.class)
class DemoServiceTest {

    @Mock
    private DemoProperties demoProperties;

    private DemoService demoService;

    @BeforeEach
    void setUp() {
        demoService = new DemoServiceImpl(demoProperties);
    }

    @Test
    void testGetDemoMessage() {
        // Given
        when(demoProperties.getName()).thenReturn("test-demo");
        when(demoProperties.isEnabled()).thenReturn(true);
        when(demoProperties.getTimeout()).thenReturn(5000L);

        // When
        String message = demoService.getDemoMessage();

        // Then
        assertNotNull(message);
        assertTrue(message.contains("test-demo"));
        assertTrue(message.contains("true"));
        assertTrue(message.contains("5000ms"));
    }

    @Test
    void testGetServiceStatus() {
        // Given
        when(demoProperties.isEnabled()).thenReturn(true);
        when(demoProperties.getName()).thenReturn("test-demo");
        
        DemoProperties.Pool pool = new DemoProperties.Pool();
        pool.setInitialSize(5);
        pool.setMaxActive(20);
        when(demoProperties.getPool()).thenReturn(pool);

        // When
        Map<String, Object> status = demoService.getServiceStatus();

        // Then
        assertNotNull(status);
        assertEquals("HillOS Demo Starter", status.get("serviceName"));
        assertEquals("2.0.0-SNAPSHOT", status.get("version"));
        assertEquals(true, status.get("enabled"));
        assertEquals("test-demo", status.get("configName"));
        assertTrue(status.containsKey("startTime"));
        assertTrue(status.containsKey("uptime"));
        assertTrue(status.containsKey("pool"));
    }

    @Test
    void testExecuteDemoWhenEnabled() {
        // Given
        when(demoProperties.isEnabled()).thenReturn(true);
        when(demoProperties.getName()).thenReturn("test-demo");

        // When & Then
        String pingResult = demoService.executeDemo("ping");
        assertEquals("pong from test-demo", pingResult);

        String echoResult = demoService.executeDemo("echo", "hello");
        assertEquals("Echo: hello", echoResult);

        String statusResult = demoService.executeDemo("status");
        assertTrue(statusResult.contains("Service is running"));

        String randomResult = demoService.executeDemo("random");
        assertTrue(randomResult.contains("Random number:"));

        String unknownResult = demoService.executeDemo("unknown");
        assertTrue(unknownResult.contains("Unknown operation"));
    }

    @Test
    void testExecuteDemoWhenDisabled() {
        // Given
        when(demoProperties.isEnabled()).thenReturn(false);

        // When
        String result = demoService.executeDemo("ping");

        // Then
        assertTrue(result.contains("Demo 功能未启用"));
    }

    @Test
    void testIsHealthyWhenEnabledAndConfigured() {
        // Given
        when(demoProperties.isEnabled()).thenReturn(true);
        when(demoProperties.getName()).thenReturn("test-demo");

        // When
        boolean healthy = demoService.isHealthy();

        // Then
        assertTrue(healthy);
    }

    @Test
    void testIsHealthyWhenDisabled() {
        // Given
        when(demoProperties.isEnabled()).thenReturn(false);

        // When
        boolean healthy = demoService.isHealthy();

        // Then
        assertFalse(healthy);
    }

    @Test
    void testIsHealthyWhenNameIsEmpty() {
        // Given
        when(demoProperties.isEnabled()).thenReturn(true);
        when(demoProperties.getName()).thenReturn("");

        // When
        boolean healthy = demoService.isHealthy();

        // Then
        assertFalse(healthy);
    }

    @Test
    void testGetConfiguration() {
        // Given
        when(demoProperties.isEnabled()).thenReturn(true);
        when(demoProperties.getName()).thenReturn("test-demo");
        when(demoProperties.getTimeout()).thenReturn(5000L);
        when(demoProperties.isCacheEnabled()).thenReturn(true);
        when(demoProperties.getMaxRetries()).thenReturn(3);
        
        DemoProperties.Pool pool = new DemoProperties.Pool();
        pool.setInitialSize(5);
        pool.setMaxActive(20);
        pool.setMaxIdle(10);
        pool.setMinIdle(2);
        when(demoProperties.getPool()).thenReturn(pool);

        // When
        Map<String, Object> config = demoService.getConfiguration();

        // Then
        assertNotNull(config);
        assertEquals(true, config.get("enabled"));
        assertEquals("test-demo", config.get("name"));
        assertEquals(5000L, config.get("timeout"));
        assertEquals(true, config.get("cacheEnabled"));
        assertEquals(3, config.get("maxRetries"));
        
        @SuppressWarnings("unchecked")
        Map<String, Object> poolConfig = (Map<String, Object>) config.get("pool");
        assertNotNull(poolConfig);
        assertEquals(5, poolConfig.get("initialSize"));
        assertEquals(20, poolConfig.get("maxActive"));
        assertEquals(10, poolConfig.get("maxIdle"));
        assertEquals(2, poolConfig.get("minIdle"));
    }
}