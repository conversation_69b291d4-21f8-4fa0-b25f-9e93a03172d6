package com.hillstone.hillos.starter.demo;

import com.hillstone.hillos.starter.demo.service.DemoService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(classes = DemoAutoConfiguration.class)
@TestPropertySource(properties = "hillstone.demo.enabled=true")
public class DemoAutoConfigurationTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    void contextLoads() {
        assertNotNull(applicationContext);
    }

    @Test
    void demoServiceBeanIsCreated() {
        DemoService demoService = applicationContext.getBean(DemoService.class);
        assertNotNull(demoService);
        assertTrue(demoService.getDemoMessage().contains("Hello from HillOS Demo Starter! 配置名称: default-demo, 启用状态: true, 超时时间: 3000ms"));
    }
}