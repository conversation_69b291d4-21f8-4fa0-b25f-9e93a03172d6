package com.hillstone.hillos.starter.demo;

import org.springframework.boot.context.properties.ConfigurationProperties;
import lombok.Data;

/**
 * HillOS Demo Starter 配置属性类
 * 
 * <AUTHOR> AI Assistant
 * @since 2.0.0
 */
@Data
@ConfigurationProperties(prefix = "hillstone.demo")
public class DemoProperties {

    /**
     * 启用或禁用 Demo 功能
     */
    private boolean enabled = false;

    /**
     * Demo 功能的名称标识
     */
    private String name = "default-demo";

    /**
     * 操作超时时间（毫秒）
     */
    private long timeout = 3000L;

    /**
     * 是否启用缓存
     */
    private boolean cacheEnabled = false;

    /**
     * 最大重试次数
     */
    private int maxRetries = 3;

    /**
     * 连接池配置
     */
    private Pool pool = new Pool();

    /**
     * 连接池配置内部类
     */
    @Data
    public static class Pool {
        /**
         * 初始连接数
         */
        private int initialSize = 5;

        /**
         * 最大连接数
         */
        private int maxActive = 20;

        /**
         * 最大空闲连接数
         */
        private int maxIdle = 10;

        /**
         * 最小空闲连接数
         */
        private int minIdle = 2;
    }
}