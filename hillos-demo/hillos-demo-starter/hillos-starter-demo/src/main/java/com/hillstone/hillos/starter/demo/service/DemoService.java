package com.hillstone.hillos.starter.demo.service;

import java.util.Map;

/**
 * HillOS Demo Starter 核心服务接口
 * 
 * <AUTHOR> AI Assistant
 * @since 2.0.0
 */
public interface DemoService {
    
    /**
     * 获取演示消息
     * @return 演示消息
     */
    String getDemoMessage();
    
    /**
     * 获取服务状态信息
     * @return 状态信息映射
     */
    Map<String, Object> getServiceStatus();
    
    /**
     * 执行演示操作
     * @param operation 操作类型
     * @param params 操作参数
     * @return 操作结果
     */
    String executeDemo(String operation, Object... params);
    
    /**
     * 健康检查
     * @return 健康状态
     */
    boolean isHealthy();
    
    /**
     * 获取配置信息
     * @return 配置信息
     */
    Map<String, Object> getConfiguration();
}