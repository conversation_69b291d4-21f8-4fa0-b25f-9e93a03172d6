package com.hillstone.hillos.starter.demo.service.impl;

import com.hillstone.hillos.starter.demo.DemoProperties;
import com.hillstone.hillos.starter.demo.service.DemoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * HillOS Demo Starter 服务实现类
 * 
 * <AUTHOR> AI Assistant
 * @since 2.0.0
 */
@Slf4j
@Service
public class DemoServiceImpl implements DemoService {

    private final DemoProperties demoProperties;
    private final LocalDateTime startTime;

    public DemoServiceImpl(DemoProperties demoProperties) {
        this.demoProperties = demoProperties;
        this.startTime = LocalDateTime.now();
        log.info("DemoService 初始化完成，配置: enabled={}, name={}", 
            demoProperties.isEnabled(), demoProperties.getName());
    }

    @Override
    public String getDemoMessage() {
        String message = String.format(
            "Hello from HillOS Demo Starter! 配置名称: %s, 启用状态: %s, 超时时间: %dms",
            demoProperties.getName(), 
            demoProperties.isEnabled(),
            demoProperties.getTimeout()
        );
        log.debug("生成演示消息: {}", message);
        return message;
    }

    @Override
    public Map<String, Object> getServiceStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("serviceName", "HillOS Demo Starter");
        status.put("version", "2.0.0-SNAPSHOT");
        status.put("enabled", demoProperties.isEnabled());
        status.put("configName", demoProperties.getName());
        status.put("startTime", startTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        status.put("uptime", java.time.Duration.between(startTime, LocalDateTime.now()).getSeconds() + "s");
        status.put("healthy", isHealthy());
        
        // 连接池状态
        Map<String, Object> poolStatus = new HashMap<>();
        poolStatus.put("initialSize", demoProperties.getPool().getInitialSize());
        poolStatus.put("maxActive", demoProperties.getPool().getMaxActive());
        poolStatus.put("currentActive", ThreadLocalRandom.current().nextInt(1, demoProperties.getPool().getMaxActive()));
        status.put("pool", poolStatus);
        
        log.debug("获取服务状态: {}", status);
        return status;
    }

    @Override
    public String executeDemo(String operation, Object... params) {
        if (!demoProperties.isEnabled()) {
            String errorMsg = "Demo 功能未启用，无法执行操作: " + operation;
            log.warn(errorMsg);
            return errorMsg;
        }

        log.info("执行演示操作: {}, 参数: {}", operation, params);
        
        try {
            // 模拟操作延时
            Thread.sleep(100);
            
            switch (operation.toLowerCase()) {
                case "ping":
                    return "pong from " + demoProperties.getName();
                case "echo":
                    return params.length > 0 ? "Echo: " + params[0] : "Echo: (empty)";
                case "status":
                    return "Service is running, uptime: " + 
                        java.time.Duration.between(startTime, LocalDateTime.now()).getSeconds() + "s";
                case "random":
                    return "Random number: " + ThreadLocalRandom.current().nextInt(1, 1000);
                default:
                    return "Unknown operation: " + operation + ". Supported: ping, echo, status, random";
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            String errorMsg = "操作被中断: " + operation;
            log.error(errorMsg, e);
            return errorMsg;
        }
    }

    @Override
    public boolean isHealthy() {
        // 简单的健康检查逻辑
        boolean healthy = demoProperties.isEnabled() && 
                         demoProperties.getName() != null && 
                         !demoProperties.getName().trim().isEmpty();
        
        log.debug("健康检查结果: {}", healthy);
        return healthy;
    }

    @Override
    public Map<String, Object> getConfiguration() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", demoProperties.isEnabled());
        config.put("name", demoProperties.getName());
        config.put("timeout", demoProperties.getTimeout());
        config.put("cacheEnabled", demoProperties.isCacheEnabled());
        config.put("maxRetries", demoProperties.getMaxRetries());
        
        // 连接池配置
        Map<String, Object> poolConfig = new HashMap<>();
        poolConfig.put("initialSize", demoProperties.getPool().getInitialSize());
        poolConfig.put("maxActive", demoProperties.getPool().getMaxActive());
        poolConfig.put("maxIdle", demoProperties.getPool().getMaxIdle());
        poolConfig.put("minIdle", demoProperties.getPool().getMinIdle());
        config.put("pool", poolConfig);
        
        log.debug("获取配置信息: {}", config);
        return config;
    }
}