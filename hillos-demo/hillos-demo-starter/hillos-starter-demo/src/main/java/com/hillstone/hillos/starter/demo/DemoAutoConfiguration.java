package com.hillstone.hillos.starter.demo;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.hillstone.hillos.starter.demo.service.DemoService;
import com.hillstone.hillos.starter.demo.service.impl.DemoServiceImpl;

import lombok.extern.slf4j.Slf4j;

/**
 * HillOS Demo Starter 自动配置类
 * 
 * <AUTHOR> AI Assistant
 * @since 2.0.0
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(DemoProperties.class)
public class DemoAutoConfiguration {

    private final DemoProperties demoProperties;

    public DemoAutoConfiguration(DemoProperties demoProperties) {
        this.demoProperties = demoProperties;
        log.info("DemoAutoConfiguration 初始化，配置: enabled={}, name={}", demoProperties.isEnabled(),
                demoProperties.getName());
    }

    /**
     * 创建 Demo 服务 Bean
     */
    @Bean
    @ConditionalOnProperty(prefix = "hillstone.demo", name = "enabled", havingValue = "true")
    @ConditionalOnMissingBean(DemoService.class)
    public DemoService demoService() {
        log.info("创建 DemoService Bean");
        return new DemoServiceImpl(demoProperties);
    }

}