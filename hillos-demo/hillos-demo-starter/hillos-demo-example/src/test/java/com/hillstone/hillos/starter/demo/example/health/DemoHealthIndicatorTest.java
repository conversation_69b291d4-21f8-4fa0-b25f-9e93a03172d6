package com.hillstone.hillos.starter.demo.example.health;

import com.hillstone.hillos.starter.demo.service.DemoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.Status;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * DemoHealthIndicator 单元测试
 * 
 * <AUTHOR> AI Assistant
 * @since 2.0.0
 */
@ExtendWith(MockitoExtension.class)
class DemoHealthIndicatorTest {

    @Mock
    private DemoService demoService;

    private DemoHealthIndicator healthIndicator;

    @BeforeEach
    void setUp() {
        healthIndicator = new DemoHealthIndicator(demoService);
    }

    @Test
    void testHealthWhenServiceIsHealthy() {
        // Given
        when(demoService.isHealthy()).thenReturn(true);
        
        Map<String, Object> mockStatus = new HashMap<>();
        mockStatus.put("serviceName", "HillOS Demo Starter");
        mockStatus.put("enabled", true);
        mockStatus.put("configName", "test-demo");
        when(demoService.getServiceStatus()).thenReturn(mockStatus);

        // When
        Health health = healthIndicator.health();

        // Then
        assertEquals(Status.UP, health.getStatus());
        assertEquals("DemoService", health.getDetails().get("service"));
        assertEquals("UP", health.getDetails().get("status"));
        assertEquals("HillOS Demo Starter", health.getDetails().get("serviceName"));
        assertEquals(true, health.getDetails().get("enabled"));
        assertEquals("test-demo", health.getDetails().get("configName"));

        verify(demoService).isHealthy();
        verify(demoService).getServiceStatus();
    }

    @Test
    void testHealthWhenServiceIsUnhealthy() {
        // Given
        when(demoService.isHealthy()).thenReturn(false);
        
        Map<String, Object> mockStatus = new HashMap<>();
        mockStatus.put("serviceName", "HillOS Demo Starter");
        mockStatus.put("enabled", false);
        mockStatus.put("configName", "");
        when(demoService.getServiceStatus()).thenReturn(mockStatus);

        // When
        Health health = healthIndicator.health();

        // Then
        assertEquals(Status.DOWN, health.getStatus());
        assertEquals("DemoService", health.getDetails().get("service"));
        assertEquals("DOWN", health.getDetails().get("status"));
        assertEquals("服务未启用或配置不正确", health.getDetails().get("reason"));
        assertEquals("HillOS Demo Starter", health.getDetails().get("serviceName"));
        assertEquals(false, health.getDetails().get("enabled"));

        verify(demoService).isHealthy();
        verify(demoService).getServiceStatus();
    }

    @Test
    void testHealthWhenExceptionOccurs() {
        // Given
        when(demoService.isHealthy()).thenThrow(new RuntimeException("Service unavailable"));

        // When
        Health health = healthIndicator.health();

        // Then
        assertEquals(Status.DOWN, health.getStatus());
        assertEquals("DemoService", health.getDetails().get("service"));
        assertEquals("ERROR", health.getDetails().get("status"));
        assertEquals("Service unavailable", health.getDetails().get("error"));

        verify(demoService).isHealthy();
        verify(demoService, never()).getServiceStatus();
    }

    @Test
    void testHealthWhenGetStatusThrowsException() {
        // Given
        when(demoService.isHealthy()).thenReturn(true);
        when(demoService.getServiceStatus()).thenThrow(new RuntimeException("Status unavailable"));

        // When
        Health health = healthIndicator.health();

        // Then
        assertEquals(Status.DOWN, health.getStatus());
        assertEquals("DemoService", health.getDetails().get("service"));
        assertEquals("ERROR", health.getDetails().get("status"));
        assertEquals("Status unavailable", health.getDetails().get("error"));

        verify(demoService).isHealthy();
        verify(demoService).getServiceStatus();
    }
}