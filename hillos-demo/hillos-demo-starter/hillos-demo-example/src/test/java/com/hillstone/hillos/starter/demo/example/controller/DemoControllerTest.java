package com.hillstone.hillos.starter.demo.example.controller;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import com.hillstone.hillos.starter.demo.service.DemoService;

/**
 * DemoController 单元测试
 * 
 * <AUTHOR> AI Assistant
 * @since 2.0.0
 */
@WebMvcTest(DemoController.class)
class DemoControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DemoService demoService;

    @Test
    void testGetMessage() throws Exception {
        // Given
        when(demoService.getDemoMessage()).thenReturn("Hello from HillOS Demo Starter!");

        // When & Then
        mockMvc.perform(get("/api/demo/message")).andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.result").value("Hello from HillOS Demo Starter!"))
                .andExpect(jsonPath("$.timestamp").exists());
    }

    @Test
    void testGetMessageWithException() throws Exception {
        // Given
        when(demoService.getDemoMessage()).thenThrow(new RuntimeException("Service error"));

        // When & Then
        mockMvc.perform(get("/api/demo/message")).andExpect(status().isInternalServerError())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("DEMO_ERROR"))
                .andExpect(jsonPath("$.message").value("获取演示消息失败: Service error"));
    }

    @Test
    void testGetStatus() throws Exception {
        // Given
        Map<String, Object> mockStatus = new HashMap<>();
        mockStatus.put("serviceName", "HillOS Demo Starter");
        mockStatus.put("enabled", true);
        when(demoService.getServiceStatus()).thenReturn(mockStatus);

        // When & Then
        mockMvc.perform(get("/api/demo/status")).andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.result.serviceName").value("HillOS Demo Starter"))
                .andExpect(jsonPath("$.result.enabled").value(true));
    }

    @Test
    void testGetStatusWithException() throws Exception {
        // Given
        when(demoService.getServiceStatus()).thenThrow(new RuntimeException("Status error"));

        // When & Then
        mockMvc.perform(get("/api/demo/status")).andExpect(status().isInternalServerError())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("STATUS_ERROR"))
                .andExpect(jsonPath("$.message").value("获取服务状态失败: Status error"));
    }

    @Test
    void testExecuteDemoWithoutParam() throws Exception {
        // Given
        when(demoService.executeDemo(eq("ping"))).thenReturn("pong from test-demo");

        // When & Then
        mockMvc.perform(post("/api/demo/execute/ping")).andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.result").value("pong from test-demo"))
                .andExpect(jsonPath("$.operation").value("ping"))
                .andExpect(jsonPath("$.timestamp").exists());
    }

    @Test
    void testExecuteDemoWithParam() throws Exception {
        // Given
        when(demoService.executeDemo(eq("echo"), eq("hello"))).thenReturn("Echo: hello");

        // When & Then
        mockMvc.perform(post("/api/demo/execute/echo").param("param", "hello"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.result").value("Echo: hello"))
                .andExpect(jsonPath("$.operation").value("echo"))
                .andExpect(jsonPath("$.timestamp").exists());
    }

    @Test
    void testExecuteDemoWithException() throws Exception {
        // Given
        when(demoService.executeDemo(eq("error"))).thenThrow(new RuntimeException("Execute error"));

        // When & Then
        mockMvc.perform(post("/api/demo/execute/error")).andExpect(status().isInternalServerError())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("EXECUTE_ERROR"))
                .andExpect(jsonPath("$.message").value("执行操作失败: Execute error"))
                .andExpect(jsonPath("$.operation").value("error"));
    }

    @Test
    void testHealthWhenHealthy() throws Exception {
        // Given
        when(demoService.isHealthy()).thenReturn(true);

        // When & Then
        mockMvc.perform(get("/api/demo/health")).andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.result").value("UP"))
                .andExpect(jsonPath("$.healthy").value(true))
                .andExpect(jsonPath("$.timestamp").exists());
    }

    @Test
    void testHealthWhenUnhealthy() throws Exception {
        // Given
        when(demoService.isHealthy()).thenReturn(false);

        // When & Then
        mockMvc.perform(get("/api/demo/health")).andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.result").value("DOWN"))
                .andExpect(jsonPath("$.healthy").value(false))
                .andExpect(jsonPath("$.timestamp").exists());
    }

    @Test
    void testHealthWithException() throws Exception {
        // Given
        when(demoService.isHealthy()).thenThrow(new RuntimeException("Health error"));

        // When & Then
        mockMvc.perform(get("/api/demo/health")).andExpect(status().isInternalServerError())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("HEALTH_ERROR"))
                .andExpect(jsonPath("$.message").value("健康检查失败: Health error"));
    }

    @Test
    void testGetConfig() throws Exception {
        // Given
        Map<String, Object> mockConfig = new HashMap<>();
        mockConfig.put("enabled", true);
        mockConfig.put("name", "test-demo");
        mockConfig.put("timeout", 5000L);
        when(demoService.getConfiguration()).thenReturn(mockConfig);

        // When & Then
        mockMvc.perform(get("/api/demo/config")).andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.result.enabled").value(true))
                .andExpect(jsonPath("$.result.name").value("test-demo"))
                .andExpect(jsonPath("$.result.timeout").value(5000));
    }

    @Test
    void testGetConfigWithException() throws Exception {
        // Given
        when(demoService.getConfiguration()).thenThrow(new RuntimeException("Config error"));

        // When & Then
        mockMvc.perform(get("/api/demo/config")).andExpect(status().isInternalServerError())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("CONFIG_ERROR"))
                .andExpect(jsonPath("$.message").value("获取配置信息失败: Config error"));
    }
}