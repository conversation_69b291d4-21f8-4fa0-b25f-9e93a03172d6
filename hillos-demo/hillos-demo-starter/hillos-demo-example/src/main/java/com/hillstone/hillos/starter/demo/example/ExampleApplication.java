package com.hillstone.hillos.starter.demo.example;

import com.hillstone.hillos.starter.demo.service.DemoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = {
        "com.hillstone.hillos.starter.demo.example",
        "com.hillstone.hillos.starter.demo"
})
public class ExampleApplication implements CommandLineRunner {

    @Autowired
    private DemoService demoService;

    public static void main(String[] args) {
        SpringApplication.run(ExampleApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        System.out.println("DemoService message: " + demoService.getDemoMessage());
    }
}