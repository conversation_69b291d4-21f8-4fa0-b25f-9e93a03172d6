package com.hillstone.hillos.starter.demo.example.controller;

import com.hillstone.hillos.starter.demo.service.DemoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Demo Starter 示例控制器
 * 展示如何在应用中使用 HillOS Demo Starter
 * 
 * <AUTHOR> AI Assistant
 * @since 2.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/demo")
public class DemoController {

    @Autowired
    private DemoService demoService;

    /**
     * 获取演示消息
     * 
     * @return 演示消息
     */
    @GetMapping("/message")
    public ResponseEntity<Map<String, Object>> getMessage() {
        log.info("收到获取演示消息请求");
        try {
            String message = demoService.getDemoMessage();
            Map<String, Object> body = new HashMap<>();
            body.put("result", message);
            body.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(body);
        } catch (Exception e) {
            log.error("获取演示消息失败", e);
            Map<String, Object> errorBody = new HashMap<>();
            errorBody.put("code", "DEMO_ERROR");
            errorBody.put("message", "获取演示消息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorBody);
        }
    }

    /**
     * 获取服务状态
     * 
     * @return 服务状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        log.info("收到获取服务状态请求");
        try {
            Map<String, Object> status = demoService.getServiceStatus();
            Map<String, Object> body = new HashMap<>();
            body.put("result", status);
            return ResponseEntity.ok(body);
        } catch (Exception e) {
            log.error("获取服务状态失败", e);
            Map<String, Object> errorBody = new HashMap<>();
            errorBody.put("code", "STATUS_ERROR");
            errorBody.put("message", "获取服务状态失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorBody);
        }
    }

    /**
     * 执行演示操作
     * 
     * @param operation 操作类型
     * @param param 操作参数（可选）
     * @return 操作结果
     */
    @PostMapping("/execute/{operation}")
    public ResponseEntity<Map<String, Object>> executeDemo(
            @PathVariable String operation,
            @RequestParam(required = false) String param) {
        log.info("收到执行演示操作请求: operation={}, param={}", operation, param);
        try {
            String result = param != null ? 
                demoService.executeDemo(operation, param) : 
                demoService.executeDemo(operation);
            
            Map<String, Object> body = new HashMap<>();
            body.put("result", result);
            body.put("operation", operation);
            body.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(body);
        } catch (Exception e) {
            log.error("执行演示操作失败: operation={}", operation, e);
            Map<String, Object> errorBody = new HashMap<>();
            errorBody.put("code", "EXECUTE_ERROR");
            errorBody.put("message", "执行操作失败: " + e.getMessage());
            errorBody.put("operation", operation);
            return ResponseEntity.internalServerError().body(errorBody);
        }
    }

    /**
     * 健康检查
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        log.debug("收到健康检查请求");
        try {
            boolean healthy = demoService.isHealthy();
            Map<String, Object> body = new HashMap<>();
            body.put("result", healthy ? "UP" : "DOWN");
            body.put("healthy", healthy);
            body.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(body);
        } catch (Exception e) {
            log.error("健康检查失败", e);
            Map<String, Object> errorBody = new HashMap<>();
            errorBody.put("code", "HEALTH_ERROR");
            errorBody.put("message", "健康检查失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorBody);
        }
    }

    /**
     * 获取配置信息
     * 
     * @return 配置信息
     */
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> getConfig() {
        log.info("收到获取配置信息请求");
        try {
            Map<String, Object> config = demoService.getConfiguration();
            Map<String, Object> body = new HashMap<>();
            body.put("result", config);
            return ResponseEntity.ok(body);
        } catch (Exception e) {
            log.error("获取配置信息失败", e);
            Map<String, Object> errorBody = new HashMap<>();
            errorBody.put("code", "CONFIG_ERROR");
            errorBody.put("message", "获取配置信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorBody);
        }
    }
}