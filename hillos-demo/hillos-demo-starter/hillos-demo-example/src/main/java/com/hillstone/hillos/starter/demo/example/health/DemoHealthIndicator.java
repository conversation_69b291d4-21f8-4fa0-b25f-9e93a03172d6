package com.hillstone.hillos.starter.demo.example.health;

import com.hillstone.hillos.starter.demo.service.DemoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * HillOS Demo Starter 健康检查指示器
 * 
 * <AUTHOR> AI Assistant
 * @since 2.0.0
 */
@Slf4j
@Component("demoHealthIndicator")
public class DemoHealthIndicator implements HealthIndicator {

    private final DemoService demoService;

    public DemoHealthIndicator(DemoService demoService) {
        this.demoService = demoService;
    }

    @Override
    public Health health() {
        log.info("开始执行 Demo 服务健康检查...");
        try {
            log.debug("调用 demoService.isHealthy()");
            boolean isHealthy = demoService.isHealthy();
            log.debug("demoService.isHealthy() 返回: {}", isHealthy);

            log.debug("调用 demoService.getServiceStatus()");
            Map<String, Object> status = demoService.getServiceStatus();
            log.debug("demoService.getServiceStatus() 返回: {}", status);
            
            if (isHealthy) {
                log.debug("Demo 服务健康检查通过");
                return Health.up()
                    .withDetail("service", "DemoService")
                    .withDetail("status", "UP")
                    .withDetails(status)
                    .build();
            } else {
                log.warn("Demo 服务健康检查失败");
                return Health.down()
                    .withDetail("service", "DemoService")
                    .withDetail("status", "DOWN")
                    .withDetail("reason", "服务未启用或配置不正确")
                    .withDetails(status)
                    .build();
            }
        } catch (Exception e) {
            log.error("Demo 服务健康检查异常", e);
            log.info("Demo 服务健康检查异常结束。");
            return Health.down()
                .withDetail("service", "DemoService")
                .withDetail("status", "ERROR")
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}