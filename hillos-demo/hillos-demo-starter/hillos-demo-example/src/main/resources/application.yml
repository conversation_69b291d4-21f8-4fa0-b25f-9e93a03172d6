server:
  port: 8081 # 避免端口冲突

spring:
  application:
    name: hillos-demo-example

# HillOS Demo Starter 配置
hillstone:
  demo:
    enabled: true
    name: "example-demo"
    timeout: 5000
    cache-enabled: true

# 日志配置
logging:
  level:
    com.hillstone.hillos.starter.demo: DEBUG
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 健康检查配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
