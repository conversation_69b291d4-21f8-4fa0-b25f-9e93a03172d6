<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.hillstone.hillos</groupId>
    <artifactId>hillos-dependencies</artifactId>
    <version>2.1.0-RELEASE</version>
  </parent>

  <groupId>com.hillstone.hillos</groupId>
  <artifactId>hillos-demo</artifactId>
  <version>${revision}</version>
  <packaging>pom</packaging>
  <name>hillos-demo</name>
  <description>A Hillos starter module: hillos-demo.</description>

  <properties>
    <java.version>1.8</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <revision.version>2.0.0</revision.version>
    <revision.modifier>SNAPSHOT</revision.modifier>
    <revision>${revision.version}-${revision.modifier}</revision>
    <hillos.common.version>2.0.0-RELEASE</hillos.common.version>
    <hillos.rest.version>2.0.0-RELEASE</hillos.rest.version>
  </properties>

  <modules>
    <module>hillos-starter-demo</module>
    <module>hillos-demo-example</module>
  </modules>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.8.1</version>
          <configuration>
            <source>${java.version}</source>
            <target>${java.version}</target>
            <encoding>${project.build.sourceEncoding}</encoding>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.22.2</version>
          <configuration>
            <argLine>-Dfile.encoding=UTF-8</argLine>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.jacoco</groupId>
          <artifactId>jacoco-maven-plugin</artifactId>
          <version>0.8.7</version>
          <executions>
            <execution>
              <id>default-prepare-agent</id>
              <goals>
                <goal>prepare-agent</goal>
              </goals>
            </execution>
            <execution>
              <id>default-report</id>
              <goals>
                <goal>report</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <executions>
          <execution>
            <id>copy-dependencies</id>
            <phase>package</phase>
            <goals>
              <goal>copy-dependencies</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.build.directory}/lib</outputDirectory>
              <excludeScope>provided</excludeScope>
              <includeScope>runtime</includeScope>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-failsafe-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>com.outbrain.swinfra</groupId>
        <artifactId>ci-friendly-flatten-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>