# HillOS Demo Starter

这是一个基于 HillOS 平台开发的 Spring Boot Starter 项目示例，展示了如何创建符合 HillOS 技术规范的企业级 Starter 组件。

## 🚀 项目特性

- ✅ **符合 HillOS 规范**: 严格遵循 HillOS 技术标准和最佳实践
- ✅ **完整的自动配置**: 基于 Spring Boot 的条件化自动配置
- ✅ **健康检查支持**: 集成 Spring Boot Actuator 健康检查
- ✅ **配置外部化**: 支持灵活的外部配置管理
- ✅ **可观测性**: 内置日志、指标和健康监控
- ✅ **完整测试覆盖**: 包含单元测试和集成测试
- ✅ **REST API 示例**: 提供完整的 Web 接口示例

## 📁 项目结构

```
hillos-demo/
├── hillos-starter-demo/                    # Starter 核心模块
│   ├── src/main/java/
│   │   └── com/hillstone/hillos/starter/demo/
│   │       ├── DemoProperties.java         # 配置属性类
│   │       ├── DemoAutoConfiguration.java  # 自动配置类
│   │       ├── service/                    # 服务层
│   │       │   ├── DemoService.java
│   │       │   └── impl/DemoServiceImpl.java
│   │       └── health/                     # 健康检查
│   │           └── DemoHealthIndicator.java
│   ├── src/main/resources/
│   │   └── META-INF/
│   │       └── spring.factories            # 自动配置注册
│   └── src/test/java/                      # 完整单元测试
├── hillos-demo-example/                    # 示例应用模块
│   ├── src/main/java/
│   │   └── com/hillstone/hillos/starter/demo/example/
│   │       ├── ExampleApplication.java     # 启动类
│   │       └── controller/                 # REST 控制器
│   │           └── DemoController.java
│   ├── src/main/resources/
│   │   └── application.yml                 # 配置文件
│   └── src/test/java/                      # 控制器测试
├── CHECKLIST.md                            # HillOS 规范验收清单
├── sonar-project.properties                # SonarQube 配置
├── lombok.config                           # Lombok 配置
└── README.md                               # 项目文档
```

## ⚡ 快速开始

### 1. 引入依赖

在你的 Spring Boot 项目的 `pom.xml` 中添加依赖：

```xml
<dependency>
    <groupId>com.hillstone.hillos</groupId>
    <artifactId>hillos-starter-demo</artifactId>
    <version>2.0.0-SNAPSHOT</version>
</dependency>

<!-- 如果需要健康检查功能 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>
```

### 2. 配置属性

在 `application.yml` 中添加配置：

```yaml
# HillOS Demo Starter 配置
hillstone:
  demo:
    enabled: true                    # 启用 Demo 功能
    name: "my-demo"                 # 实例名称
    timeout: 5000                   # 超时时间（毫秒）
    cache-enabled: true             # 启用缓存
    max-retries: 3                  # 最大重试次数
    pool:                           # 连接池配置
      initial-size: 5
      max-active: 20
      max-idle: 10
      min-idle: 2

# 健康检查端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
```

### 3. 使用服务

```java
@RestController
public class MyController {
    
    @Autowired
    private DemoService demoService;
    
    @GetMapping("/demo")
    public String demo() {
        return demoService.getDemoMessage();
    }
    
    @GetMapping("/demo/status")
    public Map<String, Object> status() {
        return demoService.getServiceStatus();
    }
}
```

## 🔧 配置属性详解

| 属性名                             | 类型    | 默认值         | 描述                 |
| ---------------------------------- | ------- | -------------- | -------------------- |
| `hillstone.demo.enabled`           | boolean | false          | 是否启用 Demo 功能   |
| `hillstone.demo.name`              | String  | "default-demo" | Demo 功能的名称标识  |
| `hillstone.demo.timeout`           | long    | 3000           | 操作超时时间（毫秒） |
| `hillstone.demo.cache-enabled`     | boolean | false          | 是否启用缓存功能     |
| `hillstone.demo.max-retries`       | int     | 3              | 最大重试次数         |
| `hillstone.demo.pool.initial-size` | int     | 5              | 连接池初始大小       |
| `hillstone.demo.pool.max-active`   | int     | 20             | 连接池最大活跃连接数 |
| `hillstone.demo.pool.max-idle`     | int     | 10             | 连接池最大空闲连接数 |
| `hillstone.demo.pool.min-idle`     | int     | 2              | 连接池最小空闲连接数 |

## 📖 API 接口

示例应用提供以下 REST API 接口：

| 接口                            | 方法 | 描述                                       |
| ------------------------------- | ---- | ------------------------------------------ |
| `/api/demo/message`             | GET  | 获取演示消息                               |
| `/api/demo/status`              | GET  | 获取服务状态信息                           |
| `/api/demo/execute/{operation}` | POST | 执行演示操作（ping, echo, status, random） |
| `/api/demo/health`              | GET  | 健康检查                                   |
| `/api/demo/config`              | GET  | 获取配置信息                               |

## 🚀 运行示例

### 方式一：直接运行

```bash
# 构建项目
mvn clean install

# 运行示例应用
cd hillos-demo-example
mvn spring-boot:run
```

### 方式二：jar 包运行

```bash
# 构建项目
mvn clean install

# 运行 jar 包
java -jar hillos-demo-example/target/hillos-demo-example-2.0.0-SNAPSHOT.jar
```

### 访问应用

- 应用端口：`http://localhost:8081`
- 健康检查：`http://localhost:8081/actuator/health`
- API 示例：`http://localhost:8081/api/demo/message`

## 🧪 测试验证

项目包含完整的测试覆盖：

```bash
# 运行所有测试
mvn test

# 运行测试并生成覆盖率报告
mvn clean test jacoco:report

# 查看覆盖率报告
open target/site/jacoco/index.html
```

## 🔍 健康检查

启用 Actuator 后，可以通过以下端点监控服务状态：

```bash
# 应用整体健康状态
curl http://localhost:8081/actuator/health

# Demo 服务健康状态
curl http://localhost:8081/actuator/health/demoHealthIndicator
```

## 📋 开发指南

### 符合 HillOS 规范

本项目严格遵循 HillOS 技术规范：

- ✅ **命名空间隔离**: 使用 `hillstone.demo` 配置前缀
- ✅ **分层架构**: 清晰的 Interface-Application-Domain-Infrastructure 分层
- ✅ **安全实践**: 输入验证、异常处理、日志脱敏
- ✅ **可观测性**: 健康检查、指标收集、日志规范
- ✅ **测试驱动**: 单元测试覆盖率 ≥ 60%

### 验收清单

请参考 [CHECKLIST.md](CHECKLIST.md) 进行项目质量验收。

## 🏗️ 构建部署

```bash
# 编译打包
mvn clean compile

# 运行测试
mvn test

# 打包应用
mvn package

# 安装到本地仓库
mvn install

# 代码质量检查
mvn sonar:sonar
```

## 📝 许可证

本项目遵循 HillOS 平台许可协议。