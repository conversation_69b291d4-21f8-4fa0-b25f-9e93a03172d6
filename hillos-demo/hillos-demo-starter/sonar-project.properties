sonar.projectKey=hillos-demo
sonar.projectName=hillos-demo
sonar.projectVersion=-${env.SMP_REVISION_MODIFIER}
sonar.sourceEncoding=UTF-8
sonar.java.coveragePlugin=jacoco
sonar.sources=
sonar.modules=hillos-starter-demo,hillos-demo-example
hillos-starter-demo.sonar.projectName=hillos-starter-demo
hillos-starter-demo.sonar.projectBaseDir=hillos-starter-demo
hillos-starter-demo.sonar.sources=src/main/java
hillos-starter-demo.sonar.java.binaries=target/classes
hillos-demo-example.sonar.projectName=hillos-demo-example
hillos-demo-example.sonar.projectBaseDir=hillos-demo-example
hillos-demo-example.sonar.sources=src/main/java
hillos-demo-example.sonar.java.binaries=target/classes