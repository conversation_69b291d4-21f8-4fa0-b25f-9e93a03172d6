# 📄 Functional and Design Specification

<details>
<summary>💡 快速上手：如何与AI助手协作</summary>

欢迎使用AI辅助功能设计（FD）！我是您的AI助手，旨在帮助您高效、规范地完成本文档。

**您可以通过以下几种方式与我协作：**

1. **启动任务 (从零开始):**
    * **您可以说:** `"分析这份PRD[链接/内容]，帮我创建FD初稿。"`
    * **我会做:** 分析需求，提出待澄清点，并为您搭建好文档框架。

2. **驱动设计 (交互式完善):**
    * **您可以说:** `"我们来设计API：`POST /api/v1/users`，请求体包含`username`和`password`。"`
    * **或者说:** `"帮我画一个用户登录的流程图。"`
    * **我会做:** 精准地将您的设计意图更新到文档的对应章节。

3. **请求检查与审查:**
    * **您可以说:** `"请对照检查清单，帮我审查一下整个文档，看看有没有遗漏或不合规的地方。"`
    * **我会做:** 执行全面的质量和规范性检查，并以报告的形式反馈给您。

4. **同步实现:**
    * **您可以说:** `"功能开发完了，这是代码路径`[...]`，请帮我核对并更新FD，确保与最终实现一致。"`
    * **我会做:** 分析代码，找出与当前文档的差异点，并辅助您完成同步。

**我的目标是成为您的智能副驾驶，将您从繁琐的文档工作中解放出来。随时可以通过对话向我发出指令！**
</details>

## 📜 Revision History 修订历史

| 版本 (Version) | 状态 (Status) | 日期 (Date) | 作者 (Author) | 修订说明 (Description) |
| :------------- | :------------ | :---------- | :------------ | :--------------------- |
| 1.0            | Draft         | YYYY-MM-DD  | [作者姓名]    | 初始草稿创建           |
|                |               |             |               |                        |

## 1. 👤 User Scenarios 用户需求
<!-- - 提供原始需求 -->
<!-- - 描述自己的理解 -->

## 2. 📋 Function List 功能列表

### 2.1 Software Function Requirements 软件功能需求列表
<!-- - 针对PRD要求的功能需求，需要逐条针对性的说明，需要描述清楚功能的适用环境及限制。需求支持描述需要清晰、明确无歧义。 -->
<!-- - 对于不能实现的需求，需要明确提出并说明理由。 -->
<!-- - 对于部分支持的需求，需要详细列明支持的部分和不支持的部分。 -->
<!-- - FS描述的清晰和明确性将作为一个FS review通过的一个必要条件。对于描述不清晰的FS，需要先修改完善后才可继续评审。 -->
| Requirements | Engineering Comments |
| :----------- | :------------------- |
|              |                      |
|              |                      |

## 3. 🔗 Related FRs 相关FR
<!-- 请在这里说明以下问题： -->
<!-- - 本FS对应的FR； -->
<!-- - 开发分支准备基于那个版本； -->
<!-- - 相关的FR，比如该FR分成了哪几个更小的FR，或者是准备在一个分支上开发的其他FR，说明一下相互关系和影响； -->

## 4. 🏗️ Architectural Design 软件架构和总体框图
<!-- - 要有完整的架构图； -->
<!-- - 对框架图有完整的描述； -->
<!-- - 系统级别的设计，需要提供系统架构图和软件架构图； -->

## 5. 🧩 Functionality Design 模块详解
<!-- - 模块的设计思路； -->
<!-- - 有模块的数据流或逻辑判断图， 并针对数据流或逻辑判断进行； -->
<!-- - 是否涉及到日志以及日志如何记录 （操作日志 + 系统日志）； -->
<!-- - 内存是否需要控制； -->
<!-- - 数据库表的详细设计； -->
<!-- - 是否涉及到数据库表的修改， 如果涉及到表的修改， 升级是如何处理的； -->
<!-- - 安全check项，请参考`fd_guideline.md`中的清单； -->

### 5.1 Function A 功能A

#### 5.1.1 User Interface 用户接口

##### CLI
<!-- 如有新增/修改命令行操作，请描述。 -->

###### Debug
<!-- 描述所有的调试命令和相应的输出结果及其含义。包括日志文件及内容，组件执行命令及结果样例。 -->

##### RESTful API
<!-- 描述该功能提供给UI（未来可能提供给第三方）的RESTful API 接口。 -->

###### RESTful API Change Summary RESTful API变更简要描述

| 变更类型 | 变更前 | 变更后 | 变更描述 | 兼容性说明 |
| :------- | :----- | :----- | :------- | :--------- |
|          |        |        |          |            |

<!-- 填写示例: -->
<!-- 示例1: 新增API -->
<!-- | 变更类型 | 变更前 | 变更后            | 变更描述                                        | 兼容性说明 | -->
<!-- | :------- | :----- | :---------------- | :---------------------------------------------- | :--------- | -->
<!-- | 新增     | 无     | GET /api/v1/users | - 范围: 整个API<br>- 原因: 新增获取用户列表功能 | 不涉及     | -->
<!-- 示例2: 修改API URI -->
<!-- | 变更类型 | 变更前                | 变更后                | 变更描述                           | 兼容性说明                                                                     | -->
<!-- | :------- | :-------------------- | :-------------------- | :--------------------------------- | :----------------------------------------------------------------------------- | -->
<!-- | 修改     | GET /api/v1/user/{id} | GET /api/v2/user/{id} | - 范围: URI<br>- 原因: API版本升级 | 存在兼容性问题。v1版本将标记为弃用，并将在未来版本中移除。建议客户端迁移至v2。 | -->
<!-- 示例3: 修改/删除Body字段 -->
<!-- | 变更类型 | 变更前                                                                              | 变更后                                                           | 变更描述                                          | 兼容性说明                                                               | -->
<!-- | :------- | :---------------------------------------------------------------------------------- | :--------------------------------------------------------------- | :------------------------------------------------ | :----------------------------------------------------------------------- | -->
<!-- | 修改     | POST /api/v1/orders<br>Body: `{"orderId": "...", "amount": ..., "currency": "USD"}` | POST /api/v1/orders<br>Body: `{"orderId": "...", "amount": ...}` | - 范围: Body字段<br>- 原因: currency 字段不再需要 | 存在兼容性问题。旧版客户端如果继续发送currency字段，服务器将忽略该字段。 | -->

##### SYSLOG
<!-- 请列出新增加的日志，参考下列表格作为范例。这个章节需要在功能进入单元测试前完成。 -->
| 类型（事件日志/操作日志） | English | Chinese |
| :------------------------ | :------ | :------ |
|                           |         |         |

#### 5.1.2 Function Testing Considerations 黑盒测试考虑
<!-- 请给出站在用户/QA使用的角度，如何验证该功能的准确性、稳定性和易用性。 -->

#### 5.1.3 Implementation 实现方法
<!-- 请按照真实的实现方法，在这里描述清楚实现采用的协议、技术等，包括关键算法、数据结构和接口。 -->

##### Data Flow 模块示意图及流程图
<!-- 画出功能对应模块示意图，需包含模块内流程及模块间数据流/工作流，图表内容需要进行描述。 -->

##### Data Structure 关键算法及数据结构
<!-- 如设计算法，描述思路，适当添加伪代码。数据结构需要写出准确定义。包括关键消息格式定义，如Kafka传输消息格式。 -->

##### Interface Design 接口定义
<!-- 包括内部接口及外部接口。 -->

#### 5.1.4 Unit Test 白盒测试
<!-- 请根据实现的设计，给出采用白盒测试的手段验证实现的准确性的考虑。包括哪些函数和过程采用白盒测试，测试的用例设计等。 -->

### 5.2 Function A 功能B
<!-- 如本FR包含多个功能，自行参考功能A添加对应章节进行描述。 -->

## 6. 🗃️ Database Design 数据库设计
<!-- 请描述新功能所要入库数据的数据条数、数据量、数据特点（查询多还是更新多），并根据这些特点，给出所选数据库的性能评估。 -->

### 6.1 Mysql

#### 6.1.1 Database Design Consideration 数据库设计考虑
<!-- 1. 说明该模块为什么需要Mysql数据库； -->
<!-- 2. 描述清楚数据之间的关系； -->
<!-- 3. 为了数据库的性能做了哪些考虑； -->
<!-- 4. 当前存在的数据库是否需要被修改，如何解决兼容性问题； -->

#### 6.1.2 Database Detailed Design 数据库详细设计
<!-- 根据如下格式明确所有数据库表。 -->
| 表名       |                                                  |          |      |          |          |
| :--------- | :----------------------------------------------- | :------- | :--- | :------- | :------- |
| 主模块     |                                                  | 子模块   |      |          |          |
| 功能描述   |                                                  |          |      |          |          |
| 数据库类型 |                                                  | 所属DB   |      | 编码格式 | utf8mb4  |
| 最大记录数 |                                                  | 记录长度 |      |          |          |
| 字段名称   | 类型                                             | 是否为空 | key  | default  | 字段描述 |
|            |                                                  |          |      |          |          |
| 建议索引   | 此项可以先开发者自己写，后续DBreviewer可给出建议 |          |      |          |          |

### 6.2 Elasticsearch（可选）
<!-- 说明功能为什么使用ES，描述index的创建，Mapping，老化机制。 -->

### 6.3 Redis（可选）
<!-- 说明功能为什么使用redis，描述key的设计，value的数据格式。尽量设置有效期，不然可能永久存在了。 -->

### 6.4 ClickHouse（可选）
<!-- 表设计，聚合统计需求，数据老化机制。 -->

### 6.5 Minio（可选）
<!-- 说明为什么要使用minio，考虑老化机制。 -->

### 6.6 Kafka（可选）
<!-- 说明功能为什么使用kafka，描述消息体及主题等信息，考虑kafka客户端及设计规范。 -->

## 7. 🧪 Testing Considerations 测试考虑
<!-- 1. 大数据量测试时，需要保证的数量级。 -->
<!-- 2. 测试过程中， 中间环节如何debug或查看日志 -->
<!-- 3. 模块之间是否有关联， 功能的修改可能会影响到的模块 -->

## 8. 💡 Patent Consideration 专利计划
<!-- 1. 不是在网上拷贝的方案， 在当前项目中新使用的， 都罗列到这里。 Review的时候可以评审一下是否可以当做专利点。 -->
<!-- 2. 思考了两天想到的解决方案罗列下来（即使未在项目中使用） -->

## 9. 📚 References 参考资料
<!-- - 列出所有参考的文档、标准、外部链接等信息 -->
<!-- - 示例：HillOS模块开发技术规范，HSR01 -->
<!-- - 示例：RFC 2616 Hypertext Transfer Protocol -- HTTP/1.1 -->

## 10. 📖 Glossary 术语表
<!-- - 列出文档中涉及的术语、缩写及其中文定义 -->
<!-- - 示例：API：应用程序编程接口 -->
<!-- - 示例：FR：功能需求 -->

## 11. ✅ 设计检查清单

> 结构化的非功能性设计指南，引导开发者在软件设计的不同阶段关注相应的质量属性和规范要求。本清单是AI助手进行规范保障、智能推荐和质量自检的核心依据。

### 11.1 架构与概念设计

> 关注系统级决策、可扩展性、核心技术选型，为整个项目奠定基础。

#### 核心设计原则

* [ ] **整体设计原则**：复用优先、基础设施解耦、面向分布式、可集成、自诊断、测试驱动

* [ ] **DDD分层架构**：Interface-Application-Domain-Infrastructure 四层架构
* [ ] **技术栈约束**：SpringBoot 2.7.9 + JDK 8

#### 可扩展性与集群

* [ ] **横向扩展设计**：使用分布式缓存(Redis)和分布式任务调度(XxlJob)替代本地实现

* [ ] **集群环境兼容**：确保新功能在集群环境下正常、合理地工作
* [ ] **容量评估**：评估极端情况下的工作能力和可扩展性

#### 多租户与许可

* [ ] **多租户支持**：从顶层设计支持多租户，所有业务资源数据模型包含 `tenantId` 字段

* [ ] **数据隔离**：考虑业务逻辑和数据隔离策略
* [ ] **软件许可证**：明确功能是否需要额外软件许可证，评估与基础许可证的兼容性

#### 重构与测试

* [ ] **代码重构评估**：涉及老代码时，评估是在原基础上添加功能还是进行重构

* [ ] **测试驱动设计**：方案具备可测试性，考虑单元测试和集成测试实现
* [ ] **代码覆盖率**：目标达到 **60% 以上**

### 11.2 模块与接口设计

> 关注具体模块实现、API定义、以及接口层面的安全与规范。

#### 模块化设计

* [ ] **模块依赖关系**：明确模块间的 API 和依赖关系

* [ ] **跨模块通信**：描述跨模块消息传递和数据共享机制

#### REST API 规范

* [ ] **URL路径规范**：
  * 应用层：`/{service}/...`
  * Web层：`/api/{service}/...`  
  * 开放接口：`/open/{version}/{service}/...`

* [ ] **响应格式标准**：
  * 成功：`{"result": ...}`
  * 错误：`{"code": ..., "message": ...}`
* [ ] **查询标准化**：GET查询支持标准 `query` 对象（`conditions`, `limit`, `start`, `sorts`）
* [ ] **HTTP方法语义**：严格遵循 `GET`, `POST`, `PUT`, `DELETE` 语义
* [ ] **错误码规范**：遵循 "模块编号+分类编码+具体编码" 的8位字符串规则
* [ ] **多语言支持**：支持 `X-API-Language` 请求头

#### 消息通信设计

* [ ] **中间件支持**：必须支持 Kafka

* [ ] **主题命名规范**：遵循 `<namespace>.<app>.<type>.<event>` 层级规范
* [ ] **消息体标准**：包含 `id`, `timestamp`, `msgType` 等标准字段

#### 接口安全检查

* [ ] **文件操作**：是否有文件上传/下载功能？

* [ ] **新增API**：是否有新增的 RESTful API 接口？
* [ ] **SSRF风险**：是否存在服务器端请求转发？
* [ ] **权限变更**：是否更改了现有权限设计？
* [ ] **XXE风险**：是否使用系统库读写 XML 文件？

### 11.3 数据与实现规范

> 关注数据存储、编码细节、依赖管理和实现层面的安全。

#### 数据库设计

* [ ] **命名规范**：
  * 库名：`<product>_<module>`
  * 表名：`t_<module>_<entity/relation>`

* [ ] **版本管理**：使用 Flyway 进行数据库Schema版本化管理和迁移
* [ ] **字符集配置**：数据库表字符集采用 **utf8mb4**，支持所有unicode字符

#### 缓存与命名空间

* [ ] **缓存键规范**：Redis缓存键遵循 `<NAMESPACE>:<MODULE>:<ENTITY>:<KEY>` 层级规范

* [ ] **命名空间隔离**：确保代码包、YML配置、数据库表、缓存Key、消息主题、API路径的命名空间隔离

#### 依赖与多语言

* [ ] **Maven依赖管理**：第三方Jar包版本与系统统一，优先引用 `hillos-dependency`

* [ ] **多语言支持**：API返回值根据 `Accept-Language` 头决定，持久化存储考虑多语言

#### 代码级安全检查

* [ ] **注入风险**：是否存在 SQL拼接、文件系统路径拼接、Shell命令拼接？

* [ ] **敏感数据加密**：用户密码等敏感数据是否加密存储？
* [ ] **通信加密**：所有对外通信是否默认采用 **TLS/SSL** 加密？
* [ ] **第三方库安全**：是否引入新的第三方库？（需评估安全性）
* [ ] **认证流程变更**：是否修改用户登录流程或新增 Cookie？

### 11.4 部署与运维考量

> 关注最终交付物的部署、监控、调试和资源消耗。

#### 日志设计

* [ ] **日志级别规划**：明确 **INFO**, **WARN**, **ERROR** 日志的打印内容和时机

* [ ] **敏感信息保护**：确保密码等敏感信息**禁止**在日志中打印

#### 资源与性能

* [ ] **资源占用评估**：评估新功能对系统资源的占用（内存、CPU、IO、网络带宽、文件描述符）

* [ ] **性能影响分析**：评估对现有系统性能的潜在影响

#### 调试与部署

* [ ] **调试手段**：提供多样化调试手段，包括调试命令、日志说明及隐藏调试技巧

* [ ] **安装部署需求**：描述新功能的打包、安装需求（独立服务、第三方rpm等）

#### Flink Job 专项（如适用）

* [ ] **并行度配置**：考虑 Flink Job 并行度设置

* [ ] **资源占用**：评估 Slot 资源占用情况
* [ ] **反压风险**：评估潜在的反压可能性
* [ ] **序列化优化**：考虑数据序列化开销优化

---

<details>
<summary>🤖 AI Assistant Guide</summary>

**欢迎使用FD文档编写助手！**

你（AI）的核心任务是辅助开发人员，在整个功能设计（FD）的生命周期中，高效地完成文档的编写与维护。本指南定义了分阶段的协作流程和贯穿始终的通用能力。

---

### **分阶段协作流程**

#### **阶段一：需求分析与澄清 (Requirements Analysis & Clarification)**

* **目标**: 在设计开始前，辅助开发者识别并澄清产品需求（PRD）中的模糊、冲突或缺失之处。
* **人的引导**: 用户提供PRD文档或相关需求描述。
  * **示例指令**: `"请分析这份PRD，列出其中可能存在歧义或缺少细节的功能点，并生成问题列表。"`
* **你的辅助行动**:
    1. 深入分析需求文档，识别潜在的逻辑不一致、边界条件不清或技术可行性风险。
    2. 生成结构化的问题列表，供开发者与产品经理（PM）沟通。
    3. 根据澄清后的信息，更新或补充用户需求章节。

---

#### **阶段二：设计起草 (Design Drafting)**

* **目标**: 在功能开发初期，帮助开发人员快速将产品需求（PRD）、概念验证代码（POC）等初始信息，转化为一份结构化的FD初稿。
* **人的引导**: 用户提供原始材料，并发出指令。
  * **示例指令 (基于文档)**: `"分析这份PRD[链接/内容]，帮我填充FD的"用户需求"和"功能列表"章节。"`
  * **示例指令 (基于代码)**: `"我写了一个POC，路径在'[代码文件路径]'，请分析里面的实体类和API，帮我生成"数据库设计"和"RESTful API"的草稿。"`
* **你的辅助行动**:
    1. 接收并分析用户提供的材料。
    2. 提取关键信息（用户故事、功能点、数据实体、API端点等）。
    3. 将提取的信息，准确地填入FD模板对应的章节，形成一份可供讨论的初稿。

---

#### **阶段三：详细设计与完善 (Detailed Design & Refinement)**

* **目标**: 在FD初稿的基础上，通过与开发人员的实时交互，逐步完善设计细节，直至文档达到可供正式评审（FD Review）的状态。
* **人的引导**: 用户给出具体、明确的设计指令。
  * **示例指令 (设计API)**: `"我们来设计'获取用户列表'的API：GET /api/v1/users，请求参数需要支持'name'模糊查询，成功时返回用户对象数组。"`
  * **示例指令 (设计数据模型)**: `"请在数据库设计中，创建一个't_users'表，包含字段：id(BIGINT, PK), username(VARCHAR(50)), password_hash(VARCHAR(255))。"`
  * **示例指令 (绘制流程图)**: `"帮我用Mermaid画一个用户注册的流程图，依次是：用户输入信息 -> 服务端校验 -> 写入数据库 -> 返回成功。"`
* **你的辅助行动**:
    1. 解析用户的指令，定位到FD文档中需要修改的确切位置。
    2. 根据指令内容，进行精准的增、删、改操作。
    3. 如果指令信息不足，应主动追问以获取必要细节。

---

#### **阶段四：设计评审支持 (Design Review Assistance)**

* **目标**: 在正式评审前，辅助开发者准备评审材料，并对设计文档进行预审，提高评审效率和质量。
* **人的引导**: 用户触发评审准备流程。
  * **示例指令**: `"下周要进行FD评审，请为我准备一份评审材料，并对照**第11章的设计检查清单**进行预审，高亮潜在风险点。"`
* **你的辅助行动**:
    1. 依据**第11章的设计检查清单**自动进行预审，生成报告。
    2. 提取关键架构图、API列表、数据模型等，整合成清晰的评审材料。
    3. 高亮显示可能未完全遵循规范或存在逻辑风险的设计点。

---

#### **阶段五：代码同步与归档 (Code Sync & Archiving)**

* **目标**: 在功能开发完成或发生变更后，帮助开发人员快速将最终代码的实现同步回FD文档，确保文档的准确性和时效性，使其成为可靠的技术资产。
* **人的引导**: 用户指定需要同步的最终代码，触发同步流程。
  * **示例指令 (全量同步)**: `"这个功能已经上线了，请扫描'[代码目录路径]'下的代码，更新这份FD，确保它和最终实现一致。"`
  * **示例指令 (差异检查)**: `"请帮我对比一下这份FD里定义的API和'[Controller文件路径]'里的实际实现，有哪些出入？"`
* **你的辅助行动**:
    1. 分析用户指定的代码文件或目录。
    2. 逆向提取代码中的API定义、数据模型等信息。
    3. 将提取出的信息与FD文档的现有内容进行比对，找出差异点。
    4. 向用户报告差异，并根据用户的确认，自动更新FD文档。

---

#### **阶段六：测试用例生成 (Test Case Generation)**

* **目标**: 基于最终的设计文档，辅助生成测试用例，为QA团队提供支持，确保测试覆盖度。
* **人的引导**: 用户指定需要生成测试用例的功能模块。
  * **示例指令**: `"请根据'用户注册'功能的设计，生成核心的API测试用例和主要的E2E测试场景。"`
* **你的辅助行动**:
    1. 分析FD中的功能描述、业务逻辑、API接口和边界条件。
    2. 生成结构化的测试用例概要，包括测试场景、输入数据、预期输出等。
    3. 可区分单元测试、集成测试、API测试等不同层级的用例。

---

#### **阶段七：部署与运维文档生成 (Deployment & Operations Documentation)**

* **目标**: 从FD文档中提取关键信息，自动生成部署与运维所需的配套文档。
* **人的引导**: 用户请求生成相关运维文档。
  * **示例指令**: `"请为这个新服务生成一份部署清单和基于日志的常见问题排查手册。"`
* **你的辅助行动**:
    1. 从FD中提取架构图、服务依赖、配置项、日志格式等信息。
    2. 生成部署清单（Deployment Checklist）。
    3. 生成配置说明文档。
    4. 根据日志设计，生成初步的运维排错指南（Troubleshooting Guide）。

---

### **通用能力**

#### **通用能力：规范一致性保障 (Standards Compliance)**

* **目标**: 确保所有设计与产出，均严格遵守HillOS的技术规范。
* **核心依据**: **第11章的设计检查清单**是本能力行动的唯一准则。
* **你的辅助行动**:
    1. 在生成或修改任何内容时，主动对照**第11章的设计检查清单**中的相关条款。
    2. 在设计API、数据模型、消息等时，主动提示并采用符合清单规范的格式。
    3. 为每个重要的设计决策，提供符合清单中架构原则的合规性解释。
* **示例场景**:
  * **用户输入**: `"创建一个获取用户信息的API，路径是 /users/{id}"`
  * **AI回应/修正**: `"根据HSR05规范（关联检查清单11.2 模块与接口设计），服务API路径应为`/api/{service}/{collection_path}`格式。建议将路径修改为`/api/user-service/users/{id}`。"`

---

#### **通用能力：智能推荐与指导 (Smart Recommendations)**

* **目标**: 主动分析设计，提供有价值的建议，预防常见缺陷。
* **核心依据**: 基于**第11章的设计检查清单**中的最佳实践，并结合上下文进行智能推荐。
* **你的辅助行动**:
    1. 持续评估文档，当发现设计可能偏离**第11章的设计检查清单**中的推荐项时，主动预警。
    2. 在用户编写特定部分时，提供符合清单规范的高质量示例。
    3. 识别潜在的设计缺陷（如性能瓶颈、安全风险），并给出优化建议。
* **示例场景**:
  * **上下文**: 用户刚刚完成了数据库表`t_user_profile`的设计。
  * **AI主动提示**: `"注意到`t_user_profile`表中缺少通用的审计字段。根据最佳实践（关联检查清单11.3 数据与实现规范），建议增加`create_time`(DATETIME, NOT NULL, DEFAULT CURRENT_TIMESTAMP)和`update_time`(DATETIME, NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)字段，以便于追踪记录变更。"`

---

#### **通用能力：质量自检与迭代优化 (Quality Assurance & Iteration)**

* **目标**: 通过系统化的质量检查，确保FD文档自身的完整性、一致性和高质量。
* **核心依据**: **第11章的设计检查清单**是质量自检的核心标准。
* **你的辅助行动**:
    1. 当用户请求时（例如，`"请对文档进行质量自检"`），依据**第11章的设计检查清单**，逐项检查文档的完整性和合规性。
    2. 执行跨章节的一致性检查（如API定义与数据模型、流程图与模块描述是否一致）。
    3. 生成结构化的改进报告，并在用户确认后执行优化。
* **示例场景**:
  * **用户输入**: `"请对整个FD文档进行一次质量自检。"`
  * **AI回应/报告**: `"已完成自检，发现3个待办项：1. **[不一致]** 5.1.1节定义的API响应体包含`userName`字段，但6.1.2节的数据模型中对应字段为`user_name`。2. **[缺失]** 第4章架构设计缺少数据流图（违反检查清单11.1 架构与概念设计）。3. **[不合规]** 7.1节定义的日志格式缺少全局Trace ID（违反检查清单11.4 部署与运维考量）。"`

---

#### **通用能力：协作与版本管理 (Collaboration & Version Control)**

* **目标**: 在团队协作中，辅助追踪变更、记录决策，提升沟通效率。
* **你的辅助行动**:
    1. 自动检测文档版本间的差异，生成清晰的变更摘要（Change Log）。
    2. 辅助记录重要的设计决策及其动因至“决策日志”中。
    3. 在多人编辑时，检测潜在的设计冲突并提供调和建议。
    4. 依据**第11章的设计检查清单**生成评审准备报告，确保评审会议高效聚焦。
* **示例场景**:
  * **用户输入**: `"记录一下，我们决定采用Kafka而非RabbitMQ，主要原因是性能和生态系统更优。"`
  * **AI回应/行动**: `"好的，已将该决策记录到项目的`decisionLog.md`中，并附上时间戳和决策理由。"`

---

#### **通用能力：知识沉淀与复用 (Knowledge Management & Reuse)**

* **目标**: 跨项目学习，沉淀最佳实践，在合适的时机推荐成熟的解决方案。
* **你的辅助行动**:
    1. 在获得授权的前提下，学习和索引过往项目的设计文档和决策日志。
    2. 当在新项目中遇到相似的设计场景时，主动推荐经过验证的架构模式、组件或接口设计。
    3. 辅助将当前项目中的优秀实践，提炼并更新到可复用的系统模式库中。
* **示例场景**:
  * **上下文**: 用户正在设计一个新的文件上传功能。
  * **AI主动提示**: `"检测到您正在设计文件上传功能。在历史项目'Project-Storage'中，我们有一个经过安全加固和性能优化的文件分片上传方案（详情见[链接]），并且其设计模式已被记录在系统模式库中。是否需要将其作为本次设计的参考？"`

</details>
