# HillOS总体架构设计 (1.0 版）

## 修改历史

| Revision （版本） | Date （日期） | Author （作者） | Comments （修改说明）            |
| :---------------- | :------------ | :-------------- | :------------------------------- |
| 1.0.0             | 2024-02-20    | qiangwang       | 初始化                           |
| 1.0.1             | 2024-02-29    | qiangwang       | 完成整体架构初步设计             |
| 1.0.2             | 2024-03-7     | qiangwang       | 模块化设计章节中新增模块集成小节 |
| 1.0.3             | 2024-03-14    | qiangwang       | 基于评审修改完善                 |
| 1.0.4             | 2024-05-14    | qiangwang       | 新增R3模块并更新平台相关内容     |
| 1.0.5             | 2024-12-14    | qiangwang       | 更新产品配置及规范引用           |

## 目录

1.  引言
    1.1 背景
    1.2 定义
    1.3 使用场景
    1.4 名词术语
2.  架构概述
    2.1 目标
    2.2 原则
    2.3 整体设计
        2.3.1 产品视图
        2.3.2 技术视图
        2.3.3 组织视图
3.  架构设计
    3.1 架构总览
    3.2 产品配置
        3.2.1 配置模型
        3.2.2 软件清单
        3.2.3 平台类配置
        3.2.4 应用类配置
        3.2.5 产品应用
    3.3 模块化设计
        3.3.1 模块体系
        3.3.2 模块集成
    3.4 应用支撑平台
        3.4.1 总体架构
        3.4.2 API网关
        3.4.3 支撑应用
    3.5 基础设施平台
        3.5.1 设施标准
        3.5.2 设计规范
        3.5.3 服务管理
    3.6 产品构建
        3.6.1 构建步骤
        3.6.2 产品示例
4.  体系参考
    4.1 组织制度
    4.2 技术规范
    4.3 关键流程
    4.4 工具链
    4.5 制品仓库

## 1 引言

### 1.1 背景

随着公司安全管理和运营持续投入，相关安管平台产品的陆续推出，逐步遭遇如下挑战和瓶颈：
1.  多产品线重复功能建设和多头维护带来的重复投资 ;
2.  难以针对核心业务和关键技术进行持续沉淀和长期发展；
3.  跨产品间打通“烟囱式”子系统间交互和集成的成本高昂；

除以上短期迫切挑战外，从长期来看，近年来数字化浪潮变革正在驱动IT技术快速迭代升级，公司已有产品也需要加快引入云原生、微服务、DevOps、软件定义、意图编排、智能分析等新技术，以加速安全管理产品的技术创新和升级换代，从而保持其竞争力。同时，企业网络和数据安全挑战的多样化和市场同类安管产品竞争力不断提升，也要求公司安管产品向功能更丰富、性能更可靠、品质更优良的方向快速迭代。综上所述，我们迫切需要设计和打造一个能力开放、技术共享、快速集成、高效敏捷的可复用产品技术平台，通过平台化方式驱动公司安管产品的多样、高质、高效的快速迭代开发；

基于以上需求，结合公司现有StoneOS优势品牌和技术，我们设计和打造下一代安管和运营技术平台，并命名为HillOS。

### 1.2 定义

那什么是HillOS, 它的内涵和外延是什么？

HillOS定义为山石网科下一代安全管理和运营产品技术平台，通过统一技术框架和开放协同研发，聚焦平台通用能力建设和复用，构建产品开发基座，加速安全管理和运营全线产品的多样、高质、高效研发。

在基于StoneOS核心技术多年积淀后，适时推出全新HillOS技术平台，其定位如下：
*   HillOS未来将作为公司安管类产品的核心技术和关键基座，驱动公司新产品的快速落地；
*   HillOS会与StoneOS组成网络和数据安全操作系统闭环 ，即“中心和边界协同”的全网安全管理及运营闭环，全面满足市场多样化需求；
*   体系化的HillOS和StoneOS并行，进一步增强公司在网络和数据安全方面的关键技术、创新能力和产品生态。

### 1.3 使用场景

HillOS作为安全管理和运营软件技术平台，有三大目标和使用场景：
1.  全线产品的开放平台；
2.  全线产品的统一框架；
3.  全线产品的开发基座。

#### 场景1：开放平台

HillOS体系提供统一的开发规范、标准流程、工具链和制品仓库以支撑产品快速迭代。技术规范涉及模块开发、模块项目管理、模块版本发布、模块CICD集成、模块REST接口、产品声明式配置和产品版本发布等多个方面。同时也提供统一的代码仓库、持续集成平台、质量管理平台、接口文档管理平台等工具链，还包括maven、npm、rpm、chart 等制品仓库（更多见体系参考章节）。

#### 场景2：统一框架

HillOS系统通过模块开发技术规范和平台通用组件为产品研发提供了统一的技术框架，加速产品技术升级和模块化开发。技术规范包括底层基础设施约定、应用框架约定、工程化规范、REST接口规范、产品配置申明规范等；平台通用组件包括：依赖版本管理（HillOS Dependencies）、框架组件（HillOS Framework）、产品应用支撑（HillOS Core）等。

#### 场景3：开发基座

HillOS除了提供开放平台和统一框架功能外，更重要的是作为安管平台产品开发基座，通过平台化方式加速安管产品的多样、高质、高效的快速迭代和研发。 HillOS作为开发基座，除了流程、工具链和统一框架外，更重要是提供了如下三个产品化能力支撑：
1.  HillOS提供了统一的基础设施平台和管理服务，通过基础设施层和产品业务层的配置化解耦设计，以便全线产品可复用同一套跨系统的基础设施及管理服务；
2.  HillOS提供了产品应用支撑平台，可提供用户管理、权限管理、联系人管理、许可证管理、审计日志管理、备份工具、诊断工具、系统信息、系统参数等通用业务功能，通过API网关的可扩展性配置，快速集成其他业务应用；
3.  HillOS提供了丰富的可复用中台模块，这些通用模块是按照HillOS规划从现有产品中提取，并依照HillOS标准进行孵化升级，最终沉淀为可复用的中台模块。

### 1.4 名词术语

| 术语             | 名称                                          | 说明                                                                                                                                                                                                                            |
| :--------------- | :-------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| PMC              | 项目管理委员会 (Project Management Committee) | 负责HillOS体系项目运营和管理决议。                                                                                                                                                                                              |
| TSC              | 技术指导委员会 (Technical Steering Committee) | 负责HillOS整体架构设计、技术规范制定和核心技术评审。                                                                                                                                                                            |
| PDG              | 项目开发组 (Project Development Group)        | 负责模块项目整个生命周期开发和维护，包括Bug分析定位、网上问题解决等。                                                                                                                                                           |
| PUG              | 项目用户组 (Project User Group)               | 负责复用HillOS体系模块打造产品应用，并积极提供模块使用反馈。                                                                                                                                                                    |
| SMP              | 安全管理平台 (Security Management Platform)   | HillOS目标定位为公司下一代安全管理和运营平台。                                                                                                                                                                                  |
| HillOS Profile   | 产品声明式配置描述                            | 是对HillOS 体系产品进行软件定义和组态化 (Configuration)的一种方式。通过HillOS Profile和HillOS 模块体系，可以按需组装已有模块和新开发业务模块，快速打造新产品。                                                                  |
| HillOS Framework | 产品通用框架                                  | 基于SpringBoot微服务框架体系，结合安管产品需求定制通用框架组件，统一第三方组件版本依赖，为HillOS体系模块应用开发提供统一的通用技术框架能力。                                                                                    |
| HillOS Core      | 产品应用支撑平台                              | 包括产品支撑应用（HillOS Core Server）和API 网关（HillOS API Gateway）。                                                                                                                                                        |
| HillOS GMD       | 通用管理应用程序 (General Management Daemon)  | GMD 作为基础设施平台的管理服务，是基础设施平台（HillOS Infra）的核心。                                                                                                                                                          |
| HillOS Infra     | 产品基础设施平台                              | 包括操作系统、数据库、消息中间件、大数据组件和PaaS平台。                                                                                                                                                                        |
| HillOS Platform  | HillOS的广义名称，即产品技术平台              | HillOS Platform包括产品声明式配置（HillOS Profile）、产品通用框架（HillOS Framework）、产品基础设施平台（HillOS Infra）、产品应用支撑平台（HillOS Core）、产品模块体系（HillOS Modules）、平台工具链（HillOS Tools）等6个部分。 |

## 2 架构概述

### 2.1 目标

基于HillOS需求，HillOS整体设计目标如下：
1.  设计统一的技术标准规范，以指导和规范HillOS模块体系开发；
2.  设计统一的基础设施和技术框架，以加速HillOS模块标准化和模块化组装；
3.  设计统一的产品支撑基座，以驱动全线产品平台化开发。

### 2.2 原则

基于HillOS目标，HillOS整体设计原则如下：
1.  标准化原则，HillOS整体设计需制定标准统一的技术规范、技术框架和技术平台；
2.  模块化原则，HillOS整体设计需支持模块分层、解耦和封装，以便模块化复用和快速的按需组装；
3.  体系化原则，HillOS整体设计除技术外还包括组织、流程、规范、工具链和制品仓库等体系化设计；
4.  平台化原则，HillOS整体设计面向平台化方向，包括软硬一体式系统、基础设施平台化和产品应用平台化。

综上，HillOS整体设计原则紧紧围绕设计目标，相互关系层层递进。
*(此处应有“HillOS整体设计目标和原则”图示)*

### 2.3 整体设计

#### 2.3.1 产品视图

基于产品平台化目标，HillOS平台采用模块化和组态设计，从而类似搭乐高积木的方式来快速组装产品。
*(此处应有“HillOS产品架构概要图”图示)*

从产品角度，HillOS平台架构重点包括如下5个部分：
1.  产品配置声明 （HillOS Product Profile），类似乐高积木图纸，是对产品进行模块化定义；
2.  产品体系模块（HillOS Modules），类似乐高积木模块，是产品可集成的功能模块；
3.  产品应用支撑平台 （HillOS Core），简称支撑平台，类似乐高积木骨架，是产品通用功能和技术支撑框架；
4.  产品基础设施平台（HillOS Infra），简称基础平台，类似乐高积木底座，是产品数据和设施底座；
5.  产品研发支撑体系（HillOS R&D System），类似乐高积木说明书和搭建辅助工具，是产品软件开发流程、规范和工具链。

#### 2.3.2 技术视图

从技术角度，HillOS作为产品技术平台，是由层次化的软件模块或组件构成，它也包括5个部分：
*(此处应有“HillOS技术架构概要图”图示)*

1.  产品应用 ，是产品专用业务功能模块，包括前端应用模块（App Frontend）和后端应用模块（App Backend）；
2.  技术中台模块，是产品通用关键功能模块，是HillOS体系内产品间可复用的业务领域模块；
3.  应用支撑平台 ，是产品平台化重要支撑组件组合，包括API 网关和产品支撑应用（HillOS Core Server）等；
4.  基础设施平台，包括操作系统层、数据基础设施层和PaaS平台层，是产品数据和设施底座；
5.  平台化支撑，包括产品配置声明 、产品研发工具链等。

#### 2.3.3 组织视图

为了保障HillOS软件平台建设工作高效、扎实推进，特从组织层面对HillOS研发的团队组织、项目周期给出框架性定义。
*(此处应有“HillOS体系组织流程图”图示)*

##### 2.3.3.1 团队组织

HillOS团队执行层设立四个组织，分别为：项目管理委员会、技术指导委员会、项目开发组、项目用户组。

*   **项目管理委员会（PMC）**
    简称管理委员会，负责HillOS体系各模块项目生命周期管理，包括上游（Upstream)导入、平台孵化(Incubating) 和下游(Downsteam)复用等关键阶段的项目管理和跟踪、HillOS项目例会组织等；
    重要角色： PMC 负责人

*   **技术指导委员会（TSC)**
    简称技术委员会，负责HillOS体系整体技术规范制定、HillOS设施支撑和工具链建设以及HillOS体系技术评审和指导，具体可包括HillOS体系架构及演进、HillOS模块开发技术规范、HillOS模块技术路线评审、HillOS管理平台搭建和运营、HillOS CICD平台搭建和运营等；
    重要角色： TSC 负责人

*   **项目开发组（PDG)**
    PDG分为平台团队和虚拟小组，平台团队负责应用支撑平台和基础设施平台开发，虚拟团队承担某一HillOS关键模块在产品线的初始孵化以及后续全生命周期迭代开发；
    重要角色：HillOS 项目负责人（PO）

*   **项目用户组（PUG)**
    PUG为虚拟组织，为HillOS模块项目下游内部产品用户，负责复用HillOS能力构建新产品，并提供使用反馈和改善建议；
    重要角色：HillOS 用户（User）

##### 2.3.3.2 项目周期

HillOS模块项目周期划分为：规划阶段、提案阶段、孵化阶段、就绪阶段、成熟阶段等5个阶段，HillOS规划的体系模块将通过项目研发的方式在各个产品线中并行迭代推进。

## 3 架构设计

### 3.1 架构总览

HillOS平台架构包括4个核心层和2个外延层，核心层包括产品应用层（Application）、产品中台层（Middle Core）、产品支撑层（Platform）、基础设施层（Infrastructure）等，外延层包括平台产品层（Platform Products）和边缘产品层（Edge Products）等。
*(此处应有“HillOS平台整体架构图”图示)*

具体架构层详情见如下部件清单：

| 系统     | 层次         | 分类                | 组件                | 说明                                                                                                                                       |
| :------- | :----------- | :------------------ | :------------------ | :----------------------------------------------------------------------------------------------------------------------------------------- |
| 平台核心 | 产品应用层   | Product Frontend UI | Web UI              | 产品专用业务UI                                                                                                                             |
|          | Application  |                     | Common UI           | 产品通用业务UI，例如：系统管理                                                                                                             |
|          |              | 通用UI组件          | Report Tool         | 报表工具                                                                                                                                   |
|          |              |                     | UI Components       | UI组件库                                                                                                                                   |
|          |              |                     | Lowcode Framework   | 低代码平台                                                                                                                                 |
|          |              | 产品后端应用        | Domain Apps         | 产品专有业务应用，每个产品各不相同                                                                                                         |
|          | 产品中台层   | Core Domain         | Config Core         | StoneOS配置管理服务                                                                                                                        |
|          | Middle Core  | 关键业务模块        | Inspection Core     | 设备巡检框架                                                                                                                               |
|          |              |                     | Logparser Core      | 日志解析框架                                                                                                                               |
|          |              |                     | Workflow Core       | 工作流框架                                                                                                                                 |
|          |              |                     | App Server          | 设备接入和管理服务                                                                                                                         |
|          |              |                     | Threat Analyze      | XDR威胁分析服务                                                                                                                            |
|          |              |                     | Asset Service       | XDR资产管理服务                                                                                                                            |
|          |              |                     | Alarm Service       | 监控告警服务                                                                                                                               |
|          |              | Tools & Engine      | AAA Mgr             | AAA认证管理模块                                                                                                                            |
|          |              | 业务支撑模块        | Library Mgr         | 特征库管理组件                                                                                                                             |
|          |              |                     | Push Message        | 消息推送组件                                                                                                                               |
|          |              |                     | SysConfig Tool      | 系统配置备份组件                                                                                                                           |
|          |              |                     | Ruel Engine         | 规则引擎（Siddhi）                                                                                                                         |
|          |              |                     | Job Engine          | 任务调度引擎（XxlJob）                                                                                                                     |
|          |              |                     | Data Process Engine | 数据处理引擎（Flink）                                                                                                                      |
|          |              |                     | Data Collector      | 数据采集器（Logstash）                                                                                                                     |
|          | 产品支撑层   | Framework           | 1. Common           | 通用模块                                                                                                                                   |
|          | Platform     | 技术框架            | 2. REST             | REST 接口规范实现                                                                                                                          |
|          |              |                     | 3. Message          | 消息中间件统一客户端                                                                                                                       |
|          |              |                     | 4. Dependencies     | Maven第三方依赖库和插件管理                                                                                                                |
|          |              | Core Application    | Common Domains      | 产品支撑应用，包括如下10个通用功能：权限认证, 系统管理, 参数管理, 许可证管理, 审计日志, 联系人管理, 推送管理, 任务管理, 备份工具, 诊断工具 |
|          |              | 产品支撑应用        |                     |                                                                                                                                            |
|          |              | GMD                 | 1. Modules          | 基础设施管理服务，包括7个模块，重点为集群部署和系统管理，其中Shell为命令行工具                                                             |
|          |              | 基础设施管理服务    | 2. Shell            |                                                                                                                                            |
|          |              | Build Tools         | 1. Packer           | 产品打包工具                                                                                                                               |
|          |              | 构建工具链          | 2. CICD             | 产品持续集成和生产平台，包括Jenkins、Sonar、制品仓库、在线文档等                                                                           |
|          | 基础设施层   | OS                  | 1. AlmaLinux        | 可支持的操作系统类型                                                                                                                       |
|          | Infra        | 操作系统            | 2. OpenEular        |                                                                                                                                            |
|          |              | PaaS                | Kubernetes          | 可支持的产品应用运行的容器化平台                                                                                                           |
|          |              | Data Store          | 1. MariaDB          | 可支持的数据库、缓存和大数据组件                                                                                                           |
|          |              | 数据库              | 2. Redis            |                                                                                                                                            |
|          |              |                     | 3. ClickHouse       |                                                                                                                                            |
|          |              |                     | 4. ElasticSearch    |                                                                                                                                            |
|          |              | Message Broker      | Kafka               | 可支持的消息中间件                                                                                                                         |
|          |              | 消息总线            |                     |                                                                                                                                            |
|          |              | DFS                 | GlusterFS/MinIO     | 可支持的分布式文件系统                                                                                                                     |
|          |              | 分布式存储          |                     |                                                                                                                                            |
| 平台外延 | 平台产品层   | 采用HillOS为        | 1. HSM              | 产品采用声明配置（Profile）组装                                                                                                            |
|          | Platform     | 基座的产品          | 2. iSource          |                                                                                                                                            |
|          | Product      |                     | 3. DSGP             |                                                                                                                                            |
|          | 边缘产品层   | 支持的网络边界      | 1. FW/IPS           | 山石防火墙                                                                                                                                 |
|          | （资源层）   | 安全类产品          | 2. ADC              | 山石应用交付控制器                                                                                                                         |
|          | Edge Product |                     | 3. WAF              | 山石应用防火墙                                                                                                                             |
|          | (Resource)   | 支持的网络威胁      | 1. FW/IPS           | 山石防火墙                                                                                                                                 |
|          |              | 检测类产品          | 2. BDS              | 山石智感                                                                                                                                   |
|          |              |                     | 3. UES              | 山石智铠                                                                                                                                   |
|          |              | 支持的数据安全      | 1. DBA              | 数据审计和防护                                                                                                                             |
|          |              | 类产品              | 2. DLP              | 数据泄露防护                                                                                                                               |
|          |              |                     | 3. DMS              | 静态数据脱敏                                                                                                                               |

### 3.2 产品配置

**注意**：如下为产品配置设计的概要介绍，详细描述可参考《HSR7 HillOS产品声明式配置规范》。

#### 3.2.1 配置模型

为了能模块化组装产品，HillOS平台设计了产品声明配置（HillOS Product Profile，简称 HillOS Profile），该配置重点面向全线产品和产品生命周期进行设计，通过声明式配置方式进行产品定义和周期管理，HillOS Profile采用YAML数据格式，具体模型如下所示：
*(此处应有“产品声明配置模型”图示)*

通过HillOS Profile定义，可以对产品进行软件定义和组态化设计，并按需进行模块化组装和预配置, 从而复用统一的技术平台和核心业务模块。HillOS Profile覆盖产品软件全生命周期，包括产品打包、安装、部署、运行和升级等各个阶段，通过贯通产品全栈完成产品在设计、开发、构建、部署和运维的一体化。

产品声明式配置（HillOS Profile) 包括产品信息清单和阶段配置声明，其中阶段声明配置又分成平台类配置和应用类配置。

#### 3.2.2 软件清单

HillOS Profile 软件清单由产品对象（product) 和平台对象（platform）两个配置描述对象构成，内容包括产品基础信息、应用组件清单和平台服务清单等定义，具体如下表所示：

| 配置类型         | 包含对象          | 对象说明                             | 对应配置文件路径                      |
| :--------------- | :---------------- | :----------------------------------- | :------------------------------------ |
| Product          | product, platform | 产品和平台组件清单描述               | /etc/hillstone/profiles.d/product.yml |
| 配置对象数据格式 | YAML格式          |                                      |                                       |
| 配置对象结构定义 |                   | 其中产品对象和平台对象具体定义如下： |                                       |


| 属性类型     | 属性项名称   | 属性项类型       | 属性值             | 选择性 | 相关说明                                                                                                                                                                                                                                                                                                           |
| :----------- | :----------- | :--------------- | :----------------- | :----- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 全局属性     | apiVersion   | string           | hillos/v1alpha1    | 必须   | Schema版本控制                                                                                                                                                                                                                                                                                                     |
|              | kind         | string           | Product            | 必须   | 配置声明类型                                                                                                                                                                                                                                                                                                       |
|              | metadata     | object           |                    | 必须   | 配置对象元数据                                                                                                                                                                                                                                                                                                     |
|              | spec         | object           |                    | 必须   | 用于嵌套其他配置对象                                                                                                                                                                                                                                                                                               |
| metadata对象 | name         | string           |                    | 必须   | 配置实例名称                                                                                                                                                                                                                                                                                                       |
| spec对象     | product      | object           | product对象        | 必须   | 产品服务对象                                                                                                                                                                                                                                                                                                       |
|              | platform     | object           | platform对象       | 必须   | 产品平台对象                                                                                                                                                                                                                                                                                                       |
| product对象  | facts        | list<fact>       | fact对象列表       | 必须   | fact对象定义：<br>- name: 名称<br>  value: 值<br>  options: 可选项<br>  description： 描述                                                                                                                                                                                                                         |
|              | services     | list<service>    | service对象列表    | 必须   | service对象定义：<br>- name: 名称<br>  components: [ 组件列表 ]<br>component对象定义<br>    - name: 组件名称<br>      package： 安装包对象<br>package对象定义：<br>    - name: 系统类型，ALMA8 或 EULER2003 <br>      version: 版本号<br>      arch:  架构类型，默认：x86_64 <br>      type: 包文件类型，默认：rpm |
|              | endpoints    | list<endpoint>   | endpoint对象列表   | 必须   | endpoint对象定义：<br>- name: 端点名称<br>  service: 服务名称<br>  type:  端点类型， 默认： LoadBalancer<br>  host: 服务域名<br>  port:  服务端口<br>  targetPort: 目标端口                                                                                                                                        |
| platform对象 | facts        | list<fact>       | fact对象列表       | 可选   | 见product中facts对象定义                                                                                                                                                                                                                                                                                           |
|              | dependencies | list<dependency> | dependency对象列表 | 可选   | dependency对象定义：<br>- name: 名称<br>  package:  安装包对象                                                                                                                                                                                                                                                     |
|              | services     | list<service>    | service对象列表    | 可选   | 见product中services对象定义                                                                                                                                                                                                                                                                                        |
|              | endpoints    | list<endpoint>   | endpoint对象列表   | 可选   | 见product中endpoints对象定义                                                                                                                                                                                                                                                                                       |

#### 3.2.3 平台类配置

平台配置支持产品的打包、安装、部署和升级，支持平台配置的控制器（Operator）包括packer、kickstart、gmd， 具体定义如下表所示：

| 配置类型         | 包含对象 | 对象说明                     | 对应配置文件路径                      |
| :--------------- | :------- | :--------------------------- | :------------------------------------ |
| Build            | build    | 打包阶段配置描述对象         | /etc/hillstone/profiles.d/build.yml   |
| Install          | install  | 系统初始安装阶段配置描述对象 | /etc/hillstone/profiles.d/install.yml |
| Deploy           | deploy   | 平台部署阶段配置描述对象     | /etc/hillstone/profiles.d/deploy.yml  |
| Upgrade          | upgrade  | 平台升级阶段配置描述对象     | /etc/hillstone/profiles.d/upgrade.yml |
| 配置对象数据格式 | YAML格式 |                              |                                       |
| 配置对象结构定义 |          | HillOS Profile对象定义       |                                       |

| 属性类型     | 属性项名称 | 属性项类型 | 属性值                                                                                                                                 | 选择性 | 相关说明             |
| :----------- | :--------- | :--------- | :------------------------------------------------------------------------------------------------------------------------------------- | :----- | :------------------- |
| 全局属性     | apiVersion | string     | hillos/v1alpha1                                                                                                                        | 必须   | Schema版本控制       |
|              | kind       | enum       | kind 枚举值定义如下:<br>Build （打包配置）<br>Install （安装配置）<br>Deploy （部署配置）<br>Upgrade（升级配置）                       | 必须   | 配置种类声明         |
|              | metadata   | object     |                                                                                                                                        | 必须   | 配置对象元数据       |
|              | spec       | object     |                                                                                                                                        | 必须   | 用于嵌套其他配置对象 |
| metadata对象 | name       | string     |                                                                                                                                        | 必须   | 配置实例名称         |
| spec对象     | product    | object     | facts（对象列表）<br>services（对象列表）<br>endpoints（对象列表）                                                                     | 必须   | 产品清单对象         |
|              | platform   | object     | facts（对象列表）<br>dependencies（对象列表）<br>services（对象列表）<br>endpoints（对象列表）                                         | 必须   | 平台清单对象         |
|              | build      | object     | params（参数列表）                                                                                                                     | 必须   | 构建配置对象         |
|              | install    | object     | set-facts（列表）<br>set-envs（列表）<br>configurations（字典）                                                                        | 必须   | 安装配置对象         |
|              | deploy     | object     | crm（对象）<br>topology（对象）<br>configurations（字典）                                                                              | 必须   | 部署配置对象         |
|              | upgrade    | object     | dependencies（对象列表）<br>services（对象列表）<br>endpoints（对象列表）<br>crm（对象）<br>topology（对象）<br>configurations（字典） | 必须   | 升级配置对象         |

#### 3.2.4 应用类配置

##### 3.2.4.1 配置模型

应用类配置声明采用SpringBoot Profile配置模型和机制，使用application.yml文件进行配置, 配置项按照 “hillstone.<module name>.<submodule name>.<item>” 层次化定义，其中 module name 为HillOS体系中项目唯一名称，各模块应用配置项通过module name进行空间隔离，且按照模块名称分目录存放。各应用模块需在启动时加载该文件，以激活相关配置。

模块应用配置application.yml示例如下：
```yaml
#### 应用配置申明示例 ####
## 命名空间 ##
hillstone:
  ## 模块名称（Module Name) ##
  hillos-api-gateway:
    ## 配置项 ##
    gateway:
        ## 路由配置 ##
      routes:
        ## 路由项：hillos-sample-asset-api ##
        hillos-sample-asset-api:
          uri: http://sample-asset-service:8088
          predicates:
            - Path=/api/sample/asset**
          filters:
            - AuthToken=true
            - RewritePath=/api/sample/asset(?<segments>.*), /sample/asset$\{segments}
```

##### 配置模块化

为了支持应用配置按照应用子模块来扩展，主配置文件支持导入各子模块配置，支持模块化的application.yml主配置示例如下：
```yaml
## 应用主配置 ##
spring:
  config:
    import:
      ## 应用子模块配置 ##
      - application-asset.yml
      - application-threat.yml
```

#### 3.2.5 产品应用

产品要支持模块化构建，需要符合如下HillOS Profile使用约定。

##### 3.2.5.1 项目约定

HillOS Profile相关配置文件存放在产品项目代码的根目录profiles下，具体目录结构示意如下：
```
产品项目
├── profiles     #产品配置申明
│    └── etc     #产品配置目录 - /etc/hillstone/profiles.d
│      ├── build.yml     #平台构建阶段配置
│      ├── deploy.yml    #平台部署阶段配置
│      ├── install.yml   #平台安装阶段配置
│      ├── product.yml   #产品配置清单
│      ├── upgrade.yml   #平台升级阶段配置
│      └── config    #模块应用配置申明
│          └── hillos-api-gateway #API网关路由配置
│          └── hillos-core  #支撑应用配置
│          └── ...    #其他模块应用配置
├── ...
└── ... # 其他目录或文件
```
HillOS Profile重点包含平台类配置和应用类配置两类Profile，平台类配置按照HillOS Profile规范定义， 应用类配置采用SpringBoot Profile规范约定。

##### 3.2.5.2 安装约定

在产品安装时，hillos-profile 统一安装到 /etc/hillstone/profiles.d 目录下。

##### 3.2.5.3 使用约定

###### 3.2.5.3.1 平台类配置

产品平台类配置是对产品平台化阶段的所需配置声明，包括产品清单配置对象：Product、打包阶段配置对象：Build、安装阶段配置对象：Install、部署阶段配置对象：Deploy、升级阶段配置对象：Upgrade等， 以上配置对象分别由平台化阶段控制器：packer、kickstart、gmd 来加载和执行。

###### 3.2.5.3.2 应用类配置

application.yml重点支持应用功能配置，例如API Gateway路由配置、HillOS-Shell命令客制化等。 application.yml 通过SpringBoot应用加载激活，加载方式在应用启动参数 spring.config.location 添加位置 /etc/hillstone/profiles.d/config/<module_name>， 并顺序优先，具体示例如下：
```bash
java ${JAVA_OPTS} -jar ${HOS_MODULE_JAR} \
--spring.config.location=file:/etc/hillstone/profiles.d/config/hillos-api-gateway/,file:/usr/local/hillstone/hillos-api-gateway/conf/
```

### 3.3 模块化设计

#### 3.3.1 模块体系

##### 3.3.1.1 技术规范

###### 3.3.1.1.1 规范概述

为了HillOS体系内各模块技术统一且能快速集成，HillOS TSC 特制订并发布了模块技术开发规范，具体内容包含如下章节。
*(此处应有“HillOS 模块开发技术规范”图示)*

本技术规范包含如下要点：
1.  模块开发需采用统一的代码工程化规范；
2.  模块开发需采用统一的技术框架，包括运行环境、中间约定、服务层框架、第三方类库和模块化约定；
3.  接口规范，包括REST API规范、消息总线设计规范和南向协议支持约定；
4.  基础设施规范，包括操作系统、数据库、消息中间件、缓存、分布式存储等；
5.  测试规范，包括单元测试、集成测试和测试覆盖要求。

###### 3.3.1.1.2 全局要求

模块设计时需满足如下全局设计规范：

**要求1.支持命名空间隔离**
为了防止模块集成命名冲突。需要隔离的空间包括模块包命名空间、模块制品命名空间、应用配置命名空间、数据库表命名空间、消息主题命名空间、缓存键命名空间等。

| 类别             | 空间隔离约定                                                               | 示例                                                |
| :--------------- | :------------------------------------------------------------------------- | :-------------------------------------------------- |
| 模块包命名空间   | com.hillstone.hillos.<module>.<submodule> (小写）                          | Java包名示例： com.hillstone.hillos.pushmessage     |
| 应用配置命名空间 | hillstone.<module>.<submodule> (小写）                                     | YAML配置示例：hillstone.hillos-api-gateway.gateway  |
| 数据库表命名空间 | t\_<module>\_<entity/relation> (小写）                                     | 表名示例：t\_core\_user                             |
| 消息主题命名空间 | <namespace>.<application/module>.<event-type/dataset>.<event/data> (小写） | 消息主题示例： fw.monitor.collect.file              |
| 缓存键命名空间   | <namespace>:<module>:<entity>:<key> (大写） HillOS模块 namespace为 HOS     | 缓存键示例： HOS:CORE:TOKEN DSGP:ASSET:CATALOG:TYPE |
| REST API命名空间 | http://{ip}/{service}/{...} 其中 service 为各个微服务/应用模块标识 (小写） | http://127.0.0.1/core/identity/user                 |

**要求2.支持统一版本依赖**
HillOS核心项目HillOS Dependencies对第三方组件及版本进行了统一定义，所有HillOS体系模块的Maven包管理必须继承HillOS Dependencies项目，以纳入平台统一管理。

**要求3.支持REST接口规范**
HillOS框架子项目HillOS REST对接口请求和响应做了工程化实现，HillOS体系模块可以复用，以减少REST规范工程化开发。

**要求4.支持多语言设计**
默认需支持中文和英文；

**要求5.支持多租户设计**
模块模型中涉及到业务资源（例如用户、设备、安全组件、资产、日志等）都需设计租户标识tenantId，单机情况下默认租户标识tenantId为0；

**要求6.支持横向扩展设计**
模块在设计时应该用分布式全局缓存（Redis）代替本地局部缓存（例如：Map表），用任务调度中心（XxlJob）替代本地定时任务，采用分布式锁，应用和基础设施需解耦（例如本地文件存储或本地调用耦合）

以上详情请参考《HSR1 HillOS模块开发技术规范》

##### 3.3.1.2 模块化

###### 3.3.1.2.1 模块解耦

**3.3.1.2.1.1 解耦原则**
1.  设施和应用分离；
2.  技术和业务松耦合；
3.  业务边界分离；

**3.3.1.2.1.2 解耦方案**
产品模块化方式重点通过领域业务解耦、技术框架解耦和设施解耦等三种方式。通过DDD设计对业务职责边界进行模块划分，各模块采用统一的通用技术框架，从而把通用技术框架和具体业务逻辑进行必要解耦和分离，同时通过采用基础设施标准化和声明式依赖注入，对模块的业务应用层和基础设施层进行解耦。
*(此处应有“模块解耦设计图”图示)*

###### 3.3.1.2.2 模块分层

**DDD分层架构**
传统后端模块采用MVC三层架构，面向过程式编程，业务建模能力不足（失血模型），导致复杂业务代码难以维护和扩展。DDD架构更关注领域建模，通过更好的领域上下文划分和业务规则封装，提高软件模块化和可复用水平，从而更易于维护和扩展。基于HillOS模块化目标，产品模块需采用DDD设计方法进行模块分层架构。
*(此处应有“模块分层架构图”图示)*

**分层架构精简**
DDD四层架构应对复杂业务更为有效，简单业务时可以对层次进行精简，精简方式可以精简应用层和精简设施层两种方式。
*(此处应有“模块分层精简图”图示)*

如果只对四层精简其中一层，有两种方式：
*   第一种：精简应用层，这种设计比较符合业务规则简单，但需基础设施解耦的场景；
*   第二种：精简基础设施层，这种对基础设施层简单或固定，但业务规则相对复杂的场景更为适合。

##### 3.3.1.3 模块矩阵

HillOS平台进过R1版本（2023-06）、R2版本（2023-12）、R3版本（2024-6）三个版本的迭代，已提取的中台模块、平台模块初步形成了完整体系，具体模块如下表所示：

| 序号 | 模块编码 | 模块名称                      | 项目名称                | 模块类别 | 集成类型    | 交付方式 | 备注说明                                | 规划版本   | 状态   |
| :--- | :------- | :---------------------------- | :---------------------- | :------- | :---------- | :------- | :-------------------------------------- | :--------- | :----- |
| 1    | H0000    | HillOS框架 - 依赖             | hillos-dependencies     | 平台基座 | Maven继承   | 依赖包   | 第三方依赖库版本管理                    | R1 2023-06 | 已发布 |
| 2    | H0001    | 设备接入管理                  | appserver               | 中台模块 | 服务进程    | 安装包   | 从云景云平台中提取                      |            |        |
| 3    | H0002    | 报表框架                      | report-tool             | 前端模块 | 服务进程    | 安装包   | 从HSM产品中提取                         |            |        |
| 4    | H0003    | StoneOS配置管理服务           | config-core             | 中台模块 | 服务进程    | 安装包   | 从HSM产品中提取                         |            |        |
| 5    | H0004    | 系统配置备份和恢复            | sysconfig-tool          | 中台模块 | Starter模块 | 依赖包   | 从HSM产品中提取                         |            |        |
| 6    | H0005    | 消息推送                      | push-message            | 中台模块 | Starter模块 | 依赖包   | R3版本将集成到产品通用应用              |            |        |
| 7    | H0006    | 北向API框架                   | api-framework           | 平台模块 | Starter模块 | 依赖包   | 从HSM产品中提取 R3版本将集成到API网关   |            |        |
| 8    | H0007    | XDR资产管理                   | asset-service           | 中台模块 | 服务进程    | 安装包   | 从智源产品中提取                        |            |        |
| 9    | H0008    | 日志解析引擎                  | logparse-core           | 中台模块 | Starter模块 | 依赖包   | 从智源产品中提取                        |            |        |
| 10   | H0009    | UI 组件库                     | ui-components           | 前端模块 | npm模块     | 依赖包   |                                         |            |        |
| 11   | H0010    | XDR威胁分析引擎               | threat-analyze          | 中台模块 | 服务进程    | 安装包   | 从智源产品中提取                        |            |        |
| 12   | H0011    | 业务工单流基础框架            | workflow-core           | 中台模块 | Starter模块 | 依赖包   | 从云景云平台中提取                      |            |        |
| 13   | H0012    | 任务管理引擎                  | task-core               | 中台模块 | 服务进程    | 安装包   | 从HSM产品中提取                         |            |        |
| 14   | H0013    | 通用消息组件                  | hillos-message          | 平台基座 | Starter模块 | 依赖包   | 从云景云平台中提取                      |            |        |
| 15   | H0014    | 特征库升级框架                | library-manager         | 中台模块 | Starter模块 | 依赖包   | 从智源产品中提取                        | R2 2023-12 | 已发布 |
| 16   | H0015    | HillOS基础平台 - 管理服务     | hillos-gmd              | 平台基座 | 服务进程    | 安装包   | 平台服务组件                            |            |        |
| 17   | H0016    | HillOS基础平台 - Shell命令行  | hillos-shell            | 平台基座 | 服务进程    | 安装包   | 平台服务组件                            |            |        |
| 18   | H0017    | HillOS支撑平台 - 打包工具     | hillos-packer           | 平台基座 | --          |          | 支持国产化操作系统和国产化硬件          |            |        |
| 19   | H0018    | HillOS支撑平台 - 任务调度系统 | hillos-xxl-job          | 平台基座 | 服务进程    | 安装包   | R3版本将集成到产品通用应用              |            |        |
| 20   | H0019    | 巡检模块基础框架              | inspection-core         | 中台模块 | Starter模块 | 依赖包   | 从云景云平台中提取                      |            |        |
| 21   | H0021    | AAA认证                       | aaa-manager             | 中台模块 | Starter模块 | 依赖包   | R3版本将集成到产品通用应用              |            |        |
| 22   | H0022    | HillOS支撑平台 - 产品通用应用 | hillos-core             | 平台基座 | 服务进程    | 安装包   | 产品通用应用会集成 H005、H018、H019模块 | R3 2024-06 | 开发中 |
| 23   | H0024    | HillOS支撑平台 - 产品配置声明 | hillos-profile          | 平台基座 | --          |          |                                         |            |        |
| 24   | H0025    | HillOS框架 - REST组件         | hillos-rest             | 平台基座 | Starter模块 | 依赖包   |                                         |            |        |
| 25   | H0027    | 操作日志模块                  | operationlog-core       | 平台基座 | Starter模块 | 依赖包   | 从云景云平台中提取                      |            |        |
| 26   | H0028    | 存储客户端                    | hillos-storage          | 平台基座 | Starter模块 | 依赖包   | DSGP新开发                              | R3 2024-06 | 开发中 |
| 27   | H0029    | 响应剧本编排引擎              | hillos-soar             | 中台模块 | Starter模块 | 依赖包   | 从智源平台中提取                        |            |        |
| 28   | H0030    | 通用报表引擎                  | hillos-report           | 中台模块 | Starter模块 | 依赖包   | DSGP新开发                              |            |        |
| 29   | H0031    | 通用告警引擎                  | hillos-alarm            | 中台模块 | Starter模块 | 依赖包   | DSGP新开发                              |            |        |
|      |          | **Total**                     | **29 （截止HillOS R3)** |          |             |          |                                         |            |        |

#### 3.3.2 模块集成

HillOS体系模块可分成三类，分别是通用组件类、功能子模块类和中台服务类模块。不同类型模块集成方式不同，组件类模块主要采用Jar包依赖方式，子模块类使用子模块集成方式，中台服务类采用微服务框架方式通过平台进行集成，具体方式示意如下:
*(此处应有“模块集成方式示意”图示)*

HillOS模块集成特点如下表所述：

| 序号 | 集成类型 | 功能特点                                                                                             | 集成方式   | 使用方式         |
| :--- | :------- | :--------------------------------------------------------------------------------------------------- | :--------- | :--------------- |
| 1    | 组件类   | 通用工具类，例如Common、REST等                                                                       | Jar包依赖  | 原生Java接口调用 |
| 2    | 子模块类 | 完整业务功能模块或通用引擎<br>支持可插拔和自动化配置<br>支持数据库等基础设施配置<br>支持REST接口集成 | 子模块集成 | 模块功能编排     |
| 3    | 中台服务 | 产品通用服务，例如配置管理模块、设备接入模块等<br>可独立运行的完整应用                               | 微服务框架 | REST接口调用     |

##### 3.3.2.1 子模块集成

为了细粒级的模块化和功能复用，HillOS体系支持通用业务或通用引擎等子模块的提取。为了更好的被上层父模块（产品应用）集成，HillOS子模块需采用SpringBoot Starter方式交付，以支持自动化配置和依赖注入，具体集成流程如下图所示：
*(此处应有“子模块集成”图示)*

集成要点：
*   子模块需提供对应Starter组件，以便父模块集成时采用插件方式进行自动化装载和配置；
*   产品上层应用父模块在集成下层子模块时，需负责整体调用流程编排和数据流处理，包括调用顺序、调用输入和输出结果处理等；
*   子模块调用返回结果支持同步或异步方式，其中，在异步方式时，上层父模块可采用消息订阅或接口回调的方式接收子模块异步处理结果；
*   子模块在设计时，需做好功能解耦、减低依赖、保持职责独立，并预留可扩展接口，支持调用时上下文传递等功能；

##### ******* 微服务框架

产品应用模块将以微服务形式运行在Kubernates平台，接下来分别从南北向功能集成和东西向服务交互两个层面介绍。

###### *******.1 功能集成

HillOS平台微服务功能集成主要通过API Gateway完成，API Gateway提供统一路由、认证鉴权和北向接口集成等功能，具体调用流程如下图所示：
*(此处应有“微服务接口功能集成”图示)*

要点如下：
*   Nginx作为平台接口负载均衡（LoadBalance）、接口反向代理（Proxy）和前端Web UI运行容器（WebServer) ，部署在集群每台物理机（Node）上，支持用户通过VIP来浮动访问平台前端界面；
*   前端Web UI通过API Gateway调用后端REST接口，API Gateway采用Kubernetes的DaemonSet部署方式在每台物理机上运行，并通过NodePort暴露8080端口供Nginx调用，同时Nginx会对后端接口服务8080端口进行反向代理为443端口供前端Web UI集成（免跨域）；
*   后端服务模块通过微服务形式运行在Kubernetes平台（包括API Gateway)，并通过Kubernetes平台内网的负载均衡供API Gateway调用。

###### *******.2 服务交互

HillOS平台后端东西向服务交互采用微服务间Service/Interface模式，通过Kubernetes平台Service发现、Kubernetes平台Service负载均衡、HillOS Profile的配置管理和基于Interface的OpenFeign客户端完成微服务间相互调用，具体调用流程如下图所示：
*(此处应有“微服务内部交互”图示)*

要点如下：
*   整体架构采用Kubernates平台作为微服务运行平台，运行的微服务包括API Gateway、HillOS Core服务和产品业务服务；
*   整体的服务配置管理使用HillOS Profile配置机制，在每台服务器系统内安装HillOS Profile并把相应配置目录挂载到应用Pod内；
*   业务应用通过Kubernates CoreDNS 和Service配置对象进行服务注册和发现；
*   业务应用通过REST接口相互访问，REST接口客户端统一采用面向Interface的OpenFeign客户端；
*   业务应用的负载均衡基于Kubernates KubeProxy内部实现；

### 3.4 应用支撑平台

产品应用支撑平台（即HillOS Core）是HillOS体系核心中枢，通过它来集成产品各个业务模块并内置产品通用业务功能，是HillOS平台化的重要组成部分。

#### 3.4.1 总体架构

产品支撑平台包括API网关（HillOS API Gateway）和产品支撑应用（HillOS Core Server）两部分。其中，API网关基于产品声明化配置（HillOS Profile）对业务应用进行配置组装和服务集成；产品支撑应用提供各产品常用的通用服务（例如：用户管理、权限管理、系统管理等），并调用基础设施服务（HillOS GMD）对产品的基础设施进行管理。
*(此处应有“API网关架构图”图示)*

#### 3.4.2 API网关

产品API网关（HillOS API Gateway）是产品模块化集成的关键，为产品后端提供统一路由、认证鉴权、应用集成、接口防护、跟踪审计等核心功能。产品API网关核心基于SpringCloud Gatway开源组件，支持接口认证、接口鉴权、接口防护、日志审计等过滤器扩展。
*(此处应有“API网关核心功能”图示)*

#### 3.4.3 支撑应用

产品支撑应用（HillOS Core Server）采用模块化设计，对外提供REST接口和相应客户端（Client)，按照模块定位不同分为系统内置功能模块和可扩展模块。内置功能模块包括：身份管理（Identity Mgt）、审计日志管理（AuditLog Mgt）、系统管理（System Mgt）、诊断工具（Debug Tool）和备份工具（Backup Tool）等；可扩展模块包括：特征库模块（Library Module）、告警模块（Licence Module）、作业模块（XxlJob Module）、消息推送模块（Push Module）等。
*(此处应有“产品支撑应用结构图”图示)*

### 3.5 基础设施平台

#### 3.5.1 设施标准

HillOS平台作为产品技术平台，除了模块化集成能力外，也为产品规范了统一的基础设施标准，重点规范了基础设施类型及其版本。

**操作系统**
HillOS平台支持的操作系统如下表：

| 操作系统  | 版本           |
| :-------- | :------------- |
| AlmaLinux | 8.9            |
| OpenEuler | 20.03  LTS SP3 |

**数据库**
HillOS平台支持的数据库和中间件的类型和版本如下表：

| 数据库存储类型 | 版本       | 备注                                                   |
| :------------- | :--------- | :----------------------------------------------------- |
| MariaDB        | 关系型     | 10.5.16 主要存储业务数据，例如：基础信息、防火墙配置等 |
| ClickHouse     | 列式型     | 22.8.19主要存储时序数据，例如：监控数据、日志数据等    |
| Redis          | 实时型     | 6.2.7主要存储缓存数据，例如：基础数据缓存              |
| ElasticSearch  | 检索型     | 7.17主要全文本检索数据，例如：日志数据                 |
| MinIO          | 分布式存储 | 2024-04主要存储特征库文件和系统配置备份等              |
| Kafka          | 消息中间件 | 3.5.2流式处理平台和消息中间件                          |

#### 3.5.2 设计规范

参考《HSR1 HillOS模块开发技术规范》基础设施相关章节。

#### 3.5.3 服务管理

##### 3.5.3.1 定义

HillOS作为系统平台，对下支持不同操作系统（开源OS或者国产OS），对上为HillOS系统模块或产品业务应用提供统一的基础设施平台（HillOS Infra) ，HillOS使用GMD服务对它们进行统一配置和管理。

GMD服务作为HillOS管理服务，是HillOS基础设施平台（HillOS Infra) 必须组件，会作为系统后台程序默认安装在每个节点上。

##### 3.5.3.2 结构

HillOS GMD总体结构图如下所示：
*(此处应有“设施管理服务结构图”图示)*

GMD作为基础平台的管理核心，主要职责涵盖操作系统、节点集群、服务应用、灾备恢复和系统安全等领域，主要功能包括：系统管理（System Mgt）、网络管理（Network Mgt）、安全管理（Secure Mgt）、部署管理（Deploy Mgt）、HA管理（HA Mgt）、集群管理（Cluster Mgt）和服务运维（Service Operation）等7个部分，并提供Shell命令行和客户端SDK。

##### 3.5.3.3 配置引擎

为了灵活支持基础设施组件的部署和配置，GMD引入Ansible作为系统配置引擎，具体设计架构如下:
*(此处应有“GMD配置引擎设计图”图示)*

设计要点：
1.  配置引擎支持软件全生命周期多个阶段的配置管理，包括打包、安装部署、集群、升级和恢复等阶段；
2.  配置引擎通过角色剧本支持系统组件、设施服务或业务应用多种形式部署和配置，例如通过RPM方式进行系统部署，通过Ambari平台进行设施部署，以及通过Kubernates平台进行业务应用部署；
3.  HillOS基础设施平台，默认使用Ansible作为底层配置引擎，负责服务器系统、Ambari平台和Kubernates平台的部署，再通过Ambari平台管理中间件服务（可选），通过Kubernates平台管理业务应用（可选）。

### 3.6 产品构建

#### 3.6.1 构建步骤

基于HillOS平台进行产品开发，有如下阶段步骤：
*(此处应有“产品构建流程图”图示)*

*   **阶段1：产品定义**
    首先，在产品需求分析和概要设计阶段，需融合HillOS平台化能力来进行产品整体架构设计，其中重点要梳理出产品可分为哪些是新业务应用模块，哪些可以复用已有的HillOS体系模块，同时提供一份完整的产品配置声明。
    在产品配置声明中要定义出该产品基础信息、产品的业务应用清单、产品平台服务组件清单以及产品支撑应用配置。
    备注：产品配置声明中关于平台类配置，可基于现有模板（参考产品示例）快速定义产品的打包、安装、部署和升级等标准化过程。

*   **阶段2：产品原型**
    在产品初始开发阶段，需要验证产品定义阶段的模块划分和总体设计。由于HillOS平台化和体系化的支撑，产品开发团队可以快速推进产品的原型验证。

*   **阶段3：模块组装**
    完成产品原型验证后，可以利用HillOS模块体系、基础平台和支撑平台来集成更多产品需要的功能。

*   **阶段4：产品构建**
    在产品构建阶段，可以利用HillOS CICD平台，按照标准流程来搭建本产品的构建流水线，完成产品ISO镜像构建。

*   **阶段5：产品迭代**
    在完成产品软件IOS构建后，接下来就是正式进入产品的例行迭代阶段，在此阶段产品开发只需关注业务功能模块开发，而产品通用功能和基础设施已通过集成HillOS平台来完成。

#### 3.6.2 产品示例

为了更好推进HillOS在产品中落地，HillOS TSC配套开发了产品示例项目（HillOS Sample）供开发者参考，具体介绍如下：

**整体架构**
*(此处应有“HillOS 产品示例架构图”图示)*

**功能用例**

| #    | 场景用例              | API 网关接口                                                                                                                                                                          | 后端服务                   |
| :--- | :-------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :------------------------- |
| 1    | 系统-系统登录         | 用户登录（验证码）<br>用户登录<br>用户登出<br>http://localhost:8080/api/core/auth/captcha <br>http://localhost:8080/api/core/auth/login<br>http://localhost:8080/api/core/auth/logout | HillOS Core                |
| 2    | 系统-用户管理         | 增删改查、查询<br>http://localhost:8080/api/core/user**                                                                                                                               | HillOS Core                |
| 3    | 系统-角色管理         | 增删改查、查询<br>http://localhost:8080/api/core/role**                                                                                                                               |                            |
| 4    | 系统-系统信息         | SN、CPU、内存、硬盘信息<br>http://localhost:8080/api/core/system**                                                                                                                    | HillOS Core <br>HillOS GMD |
| 5    | 产品应用1（资产管理） | 增删改查、查询<br>服务健康状态<br>http://localhost:8080/api/sample/asset**                                                                                                            | HillOS Sample-Asset        |
| 6    | 产品应用2（威胁管理） | 增删改查、查询<br>服务健康状态<br>http://localhost:8080/api/sample/threat**                                                                                                           | HillOS Sample-Threat       |
|      | **统计**              | **3 个应用场景**                                                                                                                                                                      | **23 个用例**              |
|      |                       | **29 个接口**                                                                                                                                                                         | **4 个后端服务**           |

**工程代码**
Gitlab地址：https://gitlab-sz.hillstonedev.com:4433/hillos/hillos-sample.git

**工程说明**
```
.
├── common             #通用类库
├── framework          #技术框架
├── profiles     #产品配置申明
│    └── etc     #产品配置目录 - /etc/hillstone/profiles.d
│      ├── build.yml     #平台构建阶段配置
│      ├── deploy.yml    #平台部署阶段配置
│      ├── install.yml   #平台安装阶段配置
│      ├── product.yml   #产品配置清单
│      ├── upgrade.yml   #平台升级阶段配置
│      └── config    #模块应用配置申明
│          └── hillos-api-gateway #API网关路由配置
│            └──application.yml
│            └──application-asset.yml
│            └──application-threat.yml
│          └── hillos-core  #支撑应用配置
│            └──db    #模块数据库初始化
│               └──migration
│                  └──R__hillos-core_import_data.sql
│            └──application.yml  #模块应用配置
│          └── ...    #其他模块应用配置
├── interface                  #服务接口
├── services                   #多应用（或模块）
│   ├── asset   #资产应用
│   └── threat  #威胁应用
│
└── ui         #产品前端
```

## 4 体系参考

HillOS研发支撑体系，包括组织制度、技术规范、关键流程、工具链和制品仓库，为了开发人员更好了解这个体系，特做如下概述。

### 4.1 组织制度

HillOS体系建设方式优先采取甄选孵化策略，HillOS所属各模块的研发流程基于公司产品统一研发流程和Git分支开发流程（Gitflow)，采用与产品线研发并行的虚拟项目管理制。HillOS组织管理下设管理委员会（PMC）、技术委员会（TSC）、开发组（PDG）和用户组（PUG），分别从日常项目管理、技术体系建设、项目开发和应用三个维度组织推进HillOS平台研发。
*(此处应有“HillOS组织结构”图示)*

在制度方面，HillOS管理委员会例行推进如下事务：
*   HillOS体系版本规划和统筹
*   HillOS版本计划及审议
*   HillOS模块项目甄选及提案审议
*   HillOS体系设施建设及运维
*   HillOS 委员会周会制度
    *   技术规范评审会议
    *   模块项目管理会议
*   HillOS 委员会年会制度
    *   HillOS年度计划和回顾

### 4.2 技术规范

截止目前（2024-02），HillOS技术委员会已制定如下关键技术规范：
1.  HSR01 HillOS模块开发技术规范
2.  HSR02 HillOS模块项目管理规范
3.  HSR03 HillOS模块发布流程和规范
4.  HSR04 HillOS模块CICD集成规范
5.  HSR05 HillOS模块REST接口规范
6.  HSR06 HillOS模块OpenAPI规范
7.  HSR07 HillOS产品声明式配置规范
8.  HSR08 HillOS产品发布流程和规范
9.  HSR09 HillOS产品开发技术规范
10. HSR10 HillOS平台CLI命令规范
11. HSR11 HillOS代码质量管理规范
12. HSR12 HillOS产品分支开发规范

### 4.3 关键流程

具体请参考《HillOS模块发布流程和规范》、《HillOS产品发布流程和规范》等规范。

### 4.4 工具链

HillOS平台工具链如下：

| 工具类型          | 用途                                                                 | 地址                                       |
| :---------------- | :------------------------------------------------------------------- | :----------------------------------------- |
| HillOS 管理平台   | 用于HillOS公开信息发布的平台（本文档和相关引用规范都已在该平台发布） | http://xwiki.hillstonedev.com (开发环境）  |
| HillOS Jekins平台 | 用于持续集成管理                                                     | http://jenkins.hillstondev.com (开发环境） |
| HillOS Sonar平台  | 用于代码质量管理                                                     | http://sonar.hillstonedev.com (开发环境）  |
| HillOS YAPI平台   | 用于接口文档管理                                                     | http://yapi.hillstonedev.com (开发环境）   |

### 4.5 制品仓库

HillOS平台制品仓库如下：

| 制品类型    | 仓库名称                        | 仓库地址                                                                  |
| :---------- | :------------------------------ | :------------------------------------------------------------------------ |
| Maven制品库 | maven-snapshot / maven-release  | 开发环境：http://10.10.10.3:8081/ <br>生产环境：http://10.200.5.222:8081/ |
| NPM制品库   | npm-hillos / npm-hillos-release |                                                                           |
| YUM制品库   | yum-hillos / yum-hillos-release |                                                                           |
| 其他        | schema-hillos                   |                                                                           |