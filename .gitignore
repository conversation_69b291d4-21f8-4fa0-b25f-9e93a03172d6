# Compiled class file
*.class

# Log file
*.log

# Package Files #
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs
hs_err_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IntelliJ IDEA
.idea
*.iws
*.iml
*.ipr
out/

# Eclipse
.classpath
.project
.settings/
.metadata

# VS Code
.vscode/

.DS_Store
**/.ci-friendly-pom.xml

# ignore project artifacts 
artifacts