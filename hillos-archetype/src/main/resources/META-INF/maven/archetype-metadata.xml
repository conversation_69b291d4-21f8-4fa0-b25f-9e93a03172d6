<?xml version="1.0" encoding="UTF-8"?>
<archetype-descriptor
        xsi:schemaLocation="https://maven.apache.org/plugins/maven-archetype-plugin/archetype-descriptor/1.1.0 https://maven.apache.org/xsd/archetype-descriptor-1.1.0.xsd"
        name="hillos-archetype"
        xmlns="https://maven.apache.org/plugins/maven-archetype-plugin/archetype-descriptor/1.1.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

    <requiredProperties>
        <requiredProperty key="groupId">
            <defaultValue>com.hillstone.hillos</defaultValue>
        </requiredProperty>
        <requiredProperty key="artifactId"/>
        <requiredProperty key="version">
            <defaultValue>1.0.0-SNAPSHOT</defaultValue>
        </requiredProperty>
        <requiredProperty key="package">
            <defaultValue>${groupId}.${artifactId.replaceAll("-", "")}</defaultValue>
        </requiredProperty>
    </requiredProperties>

    <fileSets>
        <fileSet filtered="true" packaged="true" encoding="UTF-8">
            <directory>src/main/java</directory>
            <includes>
                <include>**/*.java</include>
            </includes>
        </fileSet>
        <fileSet filtered="true" packaged="true" encoding="UTF-8">
            <directory>src/test/java</directory>
            <includes>
                <include>**/*.java</include>
            </includes>
        </fileSet>
        <fileSet filtered="true" encoding="UTF-8">
            <directory>src/main/resources</directory>
            <includes>
                <include>**/*.xml</include>
                <include>**/*.yml</include>
                <include>**/*.properties</include>
            </includes>
        </fileSet>
        <fileSet filtered="false" encoding="UTF-8">
            <directory></directory>
            <includes>
                <include>.gitignore</include>
                <include>lombok.config</include>
                <include>CHECKLIST.md</include>
                <include>README.md</include>
            </includes>
        </fileSet>
    </fileSets>
</archetype-descriptor>