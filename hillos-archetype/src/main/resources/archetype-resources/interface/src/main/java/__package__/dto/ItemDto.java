package ${package}.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import lombok.Data;

/**
 * Data Transfer Object for an Item.
 */
@Data
public class ItemDto {

  /**
   * The unique identifier of the item.
   */
  private Long id;

  /**
   * The name of the item. Must not be blank and must be less than 100 characters.
   */
  @NotBlank(message = "Name is required")
  @Size(max = 100, message = "Name must be less than 100 characters")
  private String name;

  /**
   * The description of the item. Can be null, but if present, must be less than
   * 500 characters.
   */
  @Size(max = 500, message = "Description must be less than 500 characters")
  private String description;
}