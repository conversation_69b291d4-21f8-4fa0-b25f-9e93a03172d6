package ${package}.iface;

import java.util.List;

import javax.management.Query;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.hillstone.hillos.rest.response.RestQueryResult;
import com.hillstone.hillos.rest.response.RestResult;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "Item Management", description = "API for managing items")
@RequestMapping("/api/v1/items")
@Validated
public interface ItemFacade {

  @Operation(summary = "Get all items", description = "Retrieve a list of all items, with support for pagination and filtering.")
  @GetMapping
  RestQueryResult<List<ItemDto>> getAllItems(
      @Parameter(description = "Query parameters for filtering and pagination") Query query);

  @Operation(summary = "Get item by ID", description = "Retrieve a specific item by its ID.")
  @GetMapping("/{id}")
  RestResult<ItemDto> getItemById(
      @Parameter(name = "id", description = "ID of the item to retrieve", required = true) @PathVariable("id") Long id);

  @Operation(summary = "Create a new item", description = "Create a new item.")
  @PostMapping
  RestResult<ItemDto> createItem(
      @Parameter(description = "Data for the new item", required = true) @Valid @RequestBody ItemDto request);

  @Operation(summary = "Update an existing item", description = "Update an existing item by its ID.")
  @PutMapping("/{id}")
  RestResult<ItemDto> updateItem(
      @Parameter(name = "id", description = "ID of the item to update", required = true) @PathVariable("id") Long id,
      @Parameter(description = "Updated data for the item", required = true) @Valid @RequestBody ItemDto request);

  @Operation(summary = "Delete an item by ID", description = "Delete a specific item by its ID.")
  @DeleteMapping("/{id}")
  RestResult<Void> deleteItem(
      @Parameter(name = "id", description = "ID of the item to delete", required = true) @PathVariable("id") Long id);
}