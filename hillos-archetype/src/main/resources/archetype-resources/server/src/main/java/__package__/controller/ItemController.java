package ${package}.controller;

import java.util.List;

import javax.management.Query;

import org.springframework.web.bind.annotation.RestController;

import com.hillstone.hillos.rest.response.RestQueryResult;
import com.hillstone.hillos.rest.response.RestResult;

import main.java.__package__.dto.ItemDto;
import main.java.__package__.iface.ItemFacade;

@RestController
public class ItemController implements ItemFacade {

  private final ItemService itemService;

  public ItemController(ItemService itemService) {
    this.itemService = itemService;
  }

  @Override
  public RestQueryResult<List<ItemDto>> getAllItems(Query query) {
    return itemService.getAllItems(query);
  }

  @Override
  public RestResult<ItemDto> getItemById(Long id) {
    return itemService.getItemById(id);
  }

  @Override
  public RestResult<ItemDto> createItem(ItemDto request) {
    return itemService.createItem(request);
  }

  @Override
  public RestResult<ItemDto> updateItem(Long id, ItemDto request) {
    return itemService.updateItem(id, request);
  }

  @Override
  public RestResult<Void> deleteItem(Long id) {
    return itemService.deleteItem(id);
  }
}