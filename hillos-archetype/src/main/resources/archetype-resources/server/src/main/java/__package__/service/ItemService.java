package ${package}.service;

import java.util.List;

import javax.management.Query;

import com.hillstone.hillos.rest.response.RestQueryResult;
import com.hillstone.hillos.rest.response.RestResult;

import main.java.__package__.dto.ItemDto;

/**
 * Service interface for managing items.
 */
public interface ItemService {

  /**
   * Retrieves all items based on the given query.
   *
   * @param query The query parameters for filtering and pagination.
   * @return A paged list of items.
   */
  RestQueryResult<List<ItemDto>> getAllItems(Query query);

  /**
   * Retrieves an item by its ID.
   *
   * @param id The ID of the item to retrieve.
   * @return The item DTO.
   */
  RestResult<ItemDto> getItemById(Long id);

  /**
   * Creates a new item.
   *
   * @param request The DTO containing the data for the new item.
   * @return The created item DTO.
   */
  RestResult<ItemDto> createItem(ItemDto request);

  /**
   * Updates an existing item.
   *
   * @param id The ID of the item to update.
   * @param request The DTO containing the updated data.
   * @return The updated item DTO.
   */
  RestResult<ItemDto> updateItem(Long id, ItemDto request);

  /**
   * Deletes an item by its ID.
   *
   * @param id The ID of the item to delete.
   * @return A RestResult indicating the outcome.
   */
  RestResult<Void> deleteItem(Long id);
}