# Spring Boot application configuration
spring:
  application:
    name: ${artifactId}-server
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driverClassName: org.h2.Driver
    username: sa
    password:
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: update
    show-sql: true

# Server port
server:
  port: 8080

# HillOS specific configurations can be added here
hillstone:
  ${artifactId}:
    some-property: "default-value"
