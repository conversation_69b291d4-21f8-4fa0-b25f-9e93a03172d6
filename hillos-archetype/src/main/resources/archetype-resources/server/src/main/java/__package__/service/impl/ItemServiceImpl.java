package ${package}.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import javax.management.Query;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hillstone.hillos.rest.response.RestQueryResult;
import com.hillstone.hillos.rest.response.RestResult;

import main.java.__package__.domain.entity.Item;
import main.java.__package__.dto.ItemDto;
import main.java.__package__.repository.ItemRepository;
import main.java.__package__.service.ItemService;

@Service
public class ItemServiceImpl implements ItemService {

  private final ItemRepository itemRepository;

  @Autowired
  public ItemServiceImpl(ItemRepository itemRepository) {
    this.itemRepository = itemRepository;
  }

  @Override
  @Transactional(readOnly = true)
  public RestQueryResult<List<ItemDto>> getAllItems(Query query) {
    int page = query.getStart() / query.getLimit();
    int size = query.getLimit();
    Sort sort = Sort.unsorted(); // Can be enhanced to parse query.getSorts()
    Pageable pageable = PageRequest.of(page, size, sort);

    Page<Item> itemPage = itemRepository.findAll(pageable);

    List<ItemDto> itemDtos = itemPage.getContent().stream().map(this::convertToDto)
        .collect(Collectors.toList());

    return RestQueryResult.<List<ItemDto>>builder().code(HttpStatus.OK.value()).message("Success")
        .result(itemDtos).total(itemPage.getTotalElements()).build();
  }

  @Override
  @Transactional(readOnly = true)
  public RestResult<ItemDto> getItemById(Long id) {
    Item item = itemRepository.findById(id)
        .orElseThrow(() -> new RuntimeException("Item not found with id: " + id)); // Replace with a
                                                                                   // proper
                                                                                   // exception
    return RestResult.<ItemDto>builder().code(HttpStatus.OK.value()).message("Success")
        .result(convertToDto(item)).build();
  }

  @Override
  @Transactional
  public RestResult<ItemDto> createItem(ItemDto request) {
    Item item = new Item();
    item.setName(request.getName());
    item.setDescription(request.getDescription());
    Item savedItem = itemRepository.save(item);
    return RestResult.<ItemDto>builder().code(HttpStatus.CREATED.value())
        .message("Created successfully").result(convertToDto(savedItem)).build();
  }

  @Override
  @Transactional
  public RestResult<ItemDto> updateItem(Long id, ItemDto request) {
    Item item = itemRepository.findById(id)
        .orElseThrow(() -> new RuntimeException("Item not found with id: " + id)); // Replace with a
                                                                                   // proper
                                                                                   // exception
    item.setName(request.getName());
    item.setDescription(request.getDescription());
    Item updatedItem = itemRepository.save(item);
    return RestResult.<ItemDto>builder().code(HttpStatus.OK.value()).message("Updated successfully")
        .result(convertToDto(updatedItem)).build();
  }

  @Override
  @Transactional
  public RestResult<Void> deleteItem(Long id) {
    if (!itemRepository.existsById(id)) {
      throw new RuntimeException("Item not found with id: " + id); // Replace with a proper
                                                                   // exception
    }
    itemRepository.deleteById(id);
    return RestResult.<Void>builder().code(HttpStatus.OK.value()).message("Deleted successfully")
        .build();
  }

  private ItemDto convertToDto(Item entity) {
    ItemDto dto = new ItemDto();
    dto.setId(entity.getId());
    dto.setName(entity.getName());
    dto.setDescription(entity.getDescription());
    return dto;
  }
}