# ${artifactId}

This project is a standard HillOS module generated from the `hillos-archetype`.

## Project Structure

This project follows the standard HillOS DDD (Domain-Driven Design) layered architecture:

-   **`interface`**: Defines the public API contracts (Facades and DTOs).
-   **`framework`**: Contains the domain model (Entities) and repository interfaces.
-   **`server`**: The main application module, containing business logic (Services), controllers, and the Spring Boot application entry point.

## How to Build

To build the project, run the following command from the root directory:

```bash
mvn clean package
```

## How to Run

After building, you can run the application from the `server` module's target directory:

```bash
java -jar server/target/${artifactId}-server-${version}.jar
```

The application will start on port 8080 by default.