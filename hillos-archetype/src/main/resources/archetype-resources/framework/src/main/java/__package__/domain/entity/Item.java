package ${package}.domain.entity;

import javax.persistence.*;

import lombok.Data;

/**
 * Represents an Item entity in the database.
 */
@Entity
@Table(name = "t_${artifactId.replaceAll("-", "_")}_item")
@Data
public class Item {

    /**
     * The unique identifier of the item.
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * The name of the item.
     */
    @Column(nullable = false, length = 100)
    private String name;

    /**
     * The description of the item.
     */
    @Column(length = 500)
    private String description;
}