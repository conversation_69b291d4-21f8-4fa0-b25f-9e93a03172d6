package ${package}.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import main.java.__package__.domain.entity.Item;

/**
 * Repository interface for {@link Item} instances.
 * <p>
 * Provides standard CRUD operations due to the extension of
 * {@link JpaRepository}.
 * </p>
 */
@Repository
public interface ItemRepository extends JpaRepository<Item, Long> {
  // Spring Data JPA will automatically implement basic CRUD operations.
  // Custom query methods can be added here if needed.
}