<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.hillstone.hillos</groupId>
        <artifactId>hillos-ai</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>hillos-archetype</artifactId>
    <version>2.0.0-SNAPSHOT</version>
    <packaging>maven-archetype</packaging>

    <name>hillos-archetype</name>
    <description>Maven Archetype for creating HillOS standard module projects.</description>

    <build>
        <extensions>
            <extension>
                <groupId>org.apache.maven.archetype</groupId>
                <artifactId>archetype-packaging</artifactId>
                <version>3.2.1</version>
            </extension>
        </extensions>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-archetype-plugin</artifactId>
                    <version>3.2.1</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>