# HillOS Prompts - Tasks

本文档为任务管理，包括跟踪已完成任务、当前任务、待处理任务、工作中发现的任务等

## Done Tasks 完成任务
- [x] 优化项目规则，整合多个规范文档，生成 [`project_rule_v3.md`](prompts/rules/guideline/project_rule_v3.md) (2025-06-04)
- [x] 提取 'docs/HSR/HSR05 HillOS模块REST接口规范.pdf' 信息，生成md文档 'docs/HSR/md/HSR05 HillOS模块REST接口规范.md' (2025-06-04)
- [x] 同步 Git 提交 `4dc4e09c486808865b3cbd16f4e14805d3c62881` (增强分页查询和代码覆盖率检查说明) 的优化到 `prompts/project/project_init` 的相关子提示词 (`03a_application.md`, `05_validation.md`) (2025-06-03)
- [x] 对 prompts/assistant_rule/PLANNING.md 的总体章节和结构给出优化建议 (2025-05-19)
- [x] 为AI编程主题分享课程TRAINNING.md给出优化建议 (2025-05-20)
- [x] 优化功能设计文档 (FD.md) 以更利于面向 AI 助手进行软件功能设计，创建了 FD_v2.md (2025-05-20)
- [x] 根据 `prompts/template.md` 生成 `prompt_example` 提示词指令 (2025-05-21)
- [x] 优化提示词模板 [`prompts/prompt_template.md`](prompts/prompt_template.md) 并将优化建议写入 [`prompts/prompt_template_optimization_suggestions.md`](prompts/prompt_template_optimization_suggestions.md) (2025-05-21)
- [x] 基于优化建议更新提示词模板 [`prompts/prompt_template.md`](prompts/prompt_template.md) (2025-05-21)
- [x] 结合Java技术规范 (`prompts/rules/guideline/hillos_java_guideline.md`) 更新提示词模板 (`prompts/prompt_template.md`) 中的Java示例 (2025-05-21)
- [x] 调整 [`prompts/prompt_template.md`](prompts/prompt_template.md) 的 Markdown 缩进格式 (2025-05-21)
- [x] 调整 [`prompts/prompt_template.md`](prompts/prompt_template.md) 的二级列表缩进为一个空格 (2025-05-21)
- [x] 将 [`prompts/prompt_template.md`](prompts/prompt_template.md) 改造为"CRISPE提示语生成器"元提示语，用于动态生成特定角色的CRISPE提示语 (2025-05-21)
- [x] 使用位于 `prompts/project/project_init.md` 的提示语来初始化一个新的 Hillos Java 项目，并将所有生成的代码和文件放置在 `test/` 目录下 (2025-05-21)
- [x] 重构 `project_init.md` 提示语并模块化至 `prompts/project/archetype/` 目录，优化其结构和内容 (2025-05-22)
- [x] 创建包含动画天气卡片的HTML文件，展示风、雨、太阳、雪四种天气条件的动画效果 (2025-05-25)
- [x] 创建包含动画天气卡片的HTML文件，展示风、雨、太阳、雪四种天气条件的动画效果，并列显示，深色背景，可通过JS切换 (2025-05-25)
- [x] 全面更新项目 README.md 文档 (2025-05-27)
- [x] 确保 prompts/project/project_init.md 中 application.yml 的默认H2和MySQL注释示例正确无误 (2025-05-22)
- [x] 查看 `hillos-rest` 库中的 `Query`, `RestResult`, `RestQueryResult` 接口/类，并提供说明以避免对象转换错误 (2025-05-22)
- [x] 增强 prompts/project/project_init.md 中关于测试用例编写的提示，确保AI为各类项目生成测试骨架 (2025-05-22)
- [x] 调整 prompts/project/project_init.md 中 PLANNING.md 和 TASK.md 的创建顺序，确保它们在项目根目录生成 (2025-05-22)
- [x] 在 prompts/project/project_init.md 中明确提示AI，application类型项目中的 "Foo" 示例实体名称不可更改 (2025-05-22)
- [x] 将 hillos-core 的 `${revision}` 版本管理机制更新到 prompts/project/project_init.md 提示语中 (2025-05-22)
- [x] **TASK 6: Create sub-prompt module `prompts/project/project_init/03c_jar.md`.** (2025-05-29)
- [x] 从RooCodeInc的GitHub项目看板 (URL: https://github.com/orgs/RooCodeInc/projects/1) 提取Pull Request (PR)信息，用于分析和总结项目的当前开发进展及Roadmap。 (2025-06-01)
- [x] 为本项目编写一个项目README，要求反应最新的项目背景、目标、架构和功能等必要章节。 (2025-06-12)

## Done Tasks 完成任务
- [x] 请查看本项目 project_init 相关提示语，给出你的理解 'prompts/' (2025-05-29)

## In Progress Tasks 进行中任务
- [x] 修复 `prompts/project/project_init.md` 中的 Surefire 插件版本和 ResourceNotFoundException 依赖问题 (2025-06-04)
- [ ] 规划PR数据提取与Roadmap分析任务，明确输出格式、命名规范、归档要求及数据结构 (2025-06-01)
- [ ] 从RooCode GitHub项目看板 (URL: https://github.com/orgs/RooCodeInc/projects/1/views/1?filterQuery=) 提取Pull Request (PR) 信息 (2025-06-01)
- [x] 查看 `prompts/rules/guideline/hillos_guideline_java.md`，提取核心原则生成供AI编程助手使用的 `project_rule.md` - 已创建优化版本 `project_rule_v2.md` 和分析报告 (2025-05-29)
- [ ] **TASK 7: Create sub-prompt module `prompts/project/project_init/04_readme.md`.** (2025-05-29)
- [ ] 基于 `prompts/project/archetype/` 中的模块化版本，更新 [`prompts/project/project_init.md`](prompts/project/project_init.md) (2025-05-28)
- [ ] 增强 project_init 模块的变量输入提示 (2025-05-30)
- [ ] 修复 project_init 模块 checklist 生成失败的问题 (2025-05-30)
- [ ] 修复 project_init 模块 project parent 没有继承 hillos-dependencies 且版本未指定的问题 (2025-05-30)
- [x] 评估 [`prompts/rules/guideline/CHECKLIST.md`](prompts/rules/guideline/CHECKLIST.md:14) 中关于Java项目包结构规范的准确性并提出修改建议 (2025-05-30)
- [x] 规划 `BUG.md` 文件结构 (2025-05-30)

## Discovered During Work 工作中发现的任务
- [ ] 修复 project_init 模块 project parent 没有继承 hillos-dependencies 且版本未指定的问题 (2025-05-31)
- [ ] 修复 project_init 模块部分依赖未指定版本，导致构建失败（如 springdoc-openapi-common、mysql-connector-j） (2025-05-31)
- [ ] 修复 project_init 模块 mysql-connector-j 仓库不可用，需移除或替换为可用依赖 (2025-05-31)
- [ ] 修复 project_init 模块 FooService 接口未单独成文件，导致编译报错 (2025-05-31)
- [ ] 修复 project_init 模块 RestQueryResult.total 类型不匹配，需强转为 long (2025-05-31)
- [ ] 修复 project_init 模块 spring-boot-maven-plugin 未配置 repackage，无法生成可执行 jar (2025-05-31)
- [ ] 修复 project_init 模块默认端口 8080 被占用，需修改为 8081 (2025-05-31)
- [ ] 修复 project_init 模块 Swagger UI 访问路径需明确为 /swagger-ui.html 或 /swagger-ui/index.html (2025-05-31)

## Upcoming Tasks 待处理任务
- [x] 将 'docs/HSR/HillOS总体架构设计.pdf' 转换为同名的md 文件 (2025-06-04)
- [ ] 修复 `prompts/project/project_init.md` 中的 Surefire 插件版本和 ResourceNotFoundException 依赖问题 (2025-06-04)