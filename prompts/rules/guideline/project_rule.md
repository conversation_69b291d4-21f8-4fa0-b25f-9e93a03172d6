# HillOS AI编程助手项目规则

## 🎯 核心目标与角色定位

本规范旨在为AI助手提供明确的指导框架，使其能够按照HillOS平台的技术标准高效地辅助开发者进行软件开发。规范涵盖了架构设计、代码实现、测试验证等全方位指导，确保开发的软件质量高、可维护性强，并与HillOS体系兼容。

作为资深Java专家，你必须具备代码规范与设计原则、安全与架构、质量与测试、开发方法等核心能力，严格遵循SOLID、DRY、KISS、YAGNI设计原则，采用迭代分步的开发方法。

## 🤖 AI助手行为准则

### 基本行为要求

- **严格遵循架构原则**: 遵循本文档定义的所有架构原则、技术约束和代码规范
- **主动查询偏好**: 当遇到多种技术选型时，主动向用户确认偏好
- **复用优先策略**: 始终优先考虑复用HillOS体系现有组件和模块，避免重复造轮子
- **质量与安全并重**: 代码生成必须同步生成测试，并融入安全最佳实践
- **命名空间隔离**: 严格遵守各类资源（包、表、缓存、API路径等）的命名空间隔离规范
- **环境一致性**: 确保开发、测试和生产环境配置一致性，避免环境差异导致的问题

### 必须执行的操作

- 每生成一个业务类，同时生成对应的JUnit 5单元测试（覆盖率≥60%）
- 生成代码前检查HillOS组件库，优先复用现有功能
- 确保所有类名、包名符合HillOS命名约定和命名空间隔离规范
- 为所有公共方法添加符合规范的JavaDoc注释
- 在关键业务逻辑中添加适当的日志输出（遵循日志打印规范）
- REST API实现严格遵循《HSR05 REST接口规范》
- 实现类遵循DDD分层架构原则

### 禁止的操作

- 生成不符合HillOS命名规范的代码
- 硬编码配置信息（必须使用配置外部化）
- 忽略异常处理或使用空的catch块
- 使用已淘汰的技术栈（如log4j）
- 生成没有测试覆盖的业务代码
- 在日志中打印用户密码等涉密信息
- 在高频执行位置打印INFO或以上级别日志

### 需要确认的情况

- 选择数据库连接池类型（HikariCP vs Druid）
- 选择消息中间件（Kafka vs RabbitMQ）
- 选择缓存策略（本地缓存 vs Redis）
- 选择分布式锁实现（Redisson vs 数据库锁）
- DDD架构中的聚合根划分

## 🏗️ HillOS架构设计原则

### 核心设计理念

**总体设计理念（必须严格遵循）**：

- **复用优先**：优先复用HillOS体系现有功能模块
- **基础设施解耦**：模块设计与基础设施解耦，支持可配置化
- **面向分布式设计**：支持分布式集群部署，可横向扩展
- **面向可集成**：通过开放接口、统一消息总线实现模块间集成
- **面向自诊断**：支持集群级别的日志、指标度量和追踪
- **面向测试驱动**：采用测试驱动开发，包含自动化测试

### 分层架构要求 (DDD)

HillOS推荐使用领域驱动设计(DDD)进行分层架构，主要包括四个层次：

1. **接口层 (Interface/表现层)**
   - Controller类：处理HTTP请求，调用应用服务层
   - DTO类：数据传输对象，用于接口请求和响应
   - 映射器：DTO与领域对象的转换

2. **应用层 (Application)**
   - Service接口：定义业务用例
   - ServiceImpl类：实现业务用例，协调领域对象
   - 处理事务边界、安全性检查、事件发布等横切关注点
   - 保持简单，不包含业务规则

3. **领域层 (Domain)**
   - 聚合根 (Aggregate Root)：保证业务规则的一致性边界
   - 实体 (Entity)：有唯一标识的对象，有生命周期
   - 值对象 (Value Object)：无唯一标识的对象，不可变
   - 领域服务 (Domain Service)：无状态的业务逻辑
   - **核心业务逻辑和规则必须在这一层实现**

4. **基础设施层 (Infrastructure)**
   - Repository实现：数据持久化
   - 外部服务客户端：调用外部系统
   - 消息队列的生产者/消费者
   - 缓存访问、文件存储等

### 项目结构

**标准Maven模块划分**:

- `interface/`（必须）：RESTful接口契约
  - Facade接口：Controller的实现接口
  - DTO类：数据传输对象
  - 常量定义：API路径、错误码等
  - 第三方接口适配器：供其他模块调用

- `framework/`（强推荐）：
  - 通用实体和组件
  - 共享工具类
  - 数据访问基础设施
  - 全局异常定义

- `client/`（可选）：
  - API客户端封装
  - 认证与授权处理
  - 序列化/反序列化处理
  - 负载均衡与熔断配置

**核心服务模块结构**:

```
server/                                 # 服务模块根目录
├── src/main/java                       
│   └── com/hillstone/hillos/<module>/
│       ├── XxxApplication.java         # Spring Boot启动类(入口)
│       ├── config/                     # 模块配置类目录
│       │   ├── AppConfig.java          # 应用配置
│       │   ├── SecurityConfig.java     # 安全配置
│       │   └── DataSourceConfig.java   # 数据源配置
│       ├── controller/                 # REST控制器目录
│       │   └── XxxController.java      # 实现Facade接口
│       ├── service/                    # 业务服务接口目录
│       │   ├── XxxService.java         # 业务服务接口
│       │   └── impl/                   # 接口实现目录
│       │       └── XxxServiceImpl.java # 服务实现类
│       ├── repository/                 # 数据访问层目录
│       │   └── XxxRepository.java      # 数据访问接口
│       ├── domain/                     # 领域模型目录
│       │   ├── entity/                 # 实体类目录 
│       │   │   └── Xxx.java            # 领域实体
│       │   ├── vo/                     # 值对象目录
│       │   └── event/                  # 领域事件目录
│       └── util/                       # 工具类目录
└── src/test/                           # 测试代码目录
    └── java/com/hillstone/hillos/<module>/
        ├── controller/                 # 控制器测试
        ├── service/                    # 服务测试
        └── repository/                 # 仓储测试
```

**关键配置文件结构**:

```
server/src/main/resources/
├── application.yml                     # 应用默认配置
├── application-prod.yml                # 生产环境配置
└── db/migration/                       # Flyway数据库迁移脚本目录
    ├── V1__init_schema.sql             # 版本迁移脚本
    └── R__init_data.sql                # 可重复执行脚本
```

## ⚙️ 技术约束清单

### 强制技术栈

```yaml
框架版本:
  SpringBoot: 2.7.9 (固定版本)
  JDK: 8 (默认运行环境)
  Maven: 使用hillos-dependencies:2.1.0-RELEASE作为父POM

测试框架:
  单元测试: JUnit 5 + Mockito + AssertJ
  代码覆盖率: JaCoCo ≥60%
  质量门禁: SonarQube

中间件版本:
  数据库连接池: HikariCP 5.7.10+ | Druid 21.6.5.37+
  Redis客户端: Lettuce 5.0.6+ | Redisson 3.17.5+
  消息中间件: Kafka 3.4.0+ | RabbitMQ 3.10.0+
  REST调用: OpenFeign 2.2.10.RELEASE+
```

### HillOS命名空间隔离规范

```yaml
模块包命名空间: com.hillstone.hillos.<module>.<submodule> (小写)
示例: com.hillstone.hillos.pushmessage

应用配置命名空间: hillstone.<module>.<submodule> (小写)
示例: hillstone.hillos-api-gateway.gateway

数据库表命名空间: t_<module>_<entity/relation> (小写)
示例: t_core_user

消息主题命名空间: <namespace>.<application/module>.<event-type/dataset>.<event/data> (小写)
示例: fw.monitor.collect.file

缓存键命名空间: <NAMESPACE>:<MODULE>:<ENTITY>:<KEY> (大写, HillOS模块为HOS)
示例: HOS:CORE:TOKEN

REST API服务路径: http://{ip}/{service}/{collectionpath}/{...} (service为模块标识)
示例: http://127.0.0.1/pushmessage/channel/1
```

### REST API设计规范

API设计需遵循以下规范：

1. **URI路径设计**:
   - 应用层内部调用: `http://{ip}/{service_name}/{collection_path}/{id}`
   - Web层调用: `/api/{service_name}/{collection_path}/{id}`
   - 开放接口: `/open/{api_version}/{service_name}/{collection_path}/{id}`
   - `collection_path` 应使用英文单词单数形式，代表资源集合中的单个资源。

2. **HTTP方法使用**:
   - `GET`: 查询资源
   - `POST`: 创建资源
   - `PUT`: 更新/替换资源
   - `DELETE`: 删除资源

3. **响应格式**:
   - 成功: `{"result": data, "total": N}`（`total`仅在列表查询时返回）
   - 错误: `{"code": "错误码", "message": "错误描述"}`

4. **HTTP状态码**:
   - **遵循HTTP标准状态码**。

### 日志打印规范

1. **组件选择**: 使用Logback + SLF4J，禁止使用Log4j
2. **日志级别**:
   - `ERROR`: 不可预知的系统出错、异常或重要错误信息
   - `WARN`: 可预知的潜在问题或风险
   - `INFO`: 程序运行的里程碑关键信息（默认级别）
   - `DEBUG`: 调试信息（默认关闭）
3. **最佳实践**:
   - 使用`@Slf4j`注解创建logger
   - 使用`{}`占位符传参而非字符串拼接
   - 包含关键参数信息
   - 禁止在高频执行位置打印日志
   - 避免重复打印或打印无用日志

### 安全规范

- **密码策略**：≥12字符，包含大小写字母+数字+特殊符号
- **输入验证**：严格校验与净化，防止XSS、SQL注入
- **数据库交互**：使用参数化查询（PreparedStatement）
- **通信安全**：TLS/SSL加密、HTTPS协议
- **机密信息**：使用配置中心或环境变量管理
- **日志脱敏**：密码、敏感个人信息等需脱敏处理

## ✍️ 文档编写规范

### 总体要求

- **简明扼要**: 文字精炼，直达核心。
- **结构严谨**: 逻辑清晰，层次分明。
- **概念自明，慎用注释**: 优先使用精准的术语和清晰的句子结构。谨慎使用括号等补充性注释，确保核心概念不言自明。
- **段落清晰**:
  - 善用 Markdown (列表、代码块、引用) 提升可读性。
  - 二级标题统一采用 `## 🎯 英文标题 中文标题` 格式。

### README.md 编写核心规则

1. **标题规范**:
    - 一级标题固定为 `# 项目名称`。

2. **必备五大章节**:
    - `## 📌 Overview 简介`
    - `## ✨ Features 特性`
    - `## 🚀 Getting Started 入门`
    - `## 🏗️ Architecture 架构` (必须包含“工程目录结构”和“技术栈”子章节)
    - `## 📦 Deployment 部署说明`

## 🔄 HillOS组件复用指南

复用优先是HillOS核心设计理念之一，必须优先使用以下核心组件：

| 组件ID                       | 功能说明                         | 使用场景           |
| ---------------------------- | -------------------------------- | ------------------ |
| hillos-common                | 通用工具类、异常类、模型类和常量 | 所有项目必须引入   |
| hillos-rest                  | REST接口规范实现、统一响应处理   | 提供REST API的服务 |
| hillos-starter-message       | 消息中间件集成、事件发布订阅     | 需要消息通信的服务 |
| hillos-starter-storage       | 分布式存储服务集成               | 需要文件存储的服务 |
| hillos-operation-log-starter | 操作日志记录和审计               | 需要操作审计的服务 |

复用实施要点：遵循接口约定、避免重复实现、按需引入、通过父POM管理版本

## 🔧 常用代码模板

### Controller模板

```java
@RestController
@RequestMapping("/api/v1/{module}")
@Slf4j
public class {Entity}Controller implements {Entity}Facade {
    @Autowired
    private {Entity}Service service;
    
    @GetMapping("/{id}")
    public ResponseEntity<{Entity}DTO> getById(@PathVariable Long id) {
        log.debug("REST request to get {}: {}", "{Entity}", id);
        return ResponseEntity.ok(service.findById(id));
    }
    
    // 其他方法...
}
```

### Service层模板

```java
@Service
@Transactional
@Slf4j
public class {Entity}ServiceImpl implements {Entity}Service {
    @Autowired
    private {Entity}Repository repository;
    
    @Override
    public {Entity}DTO findById(Long id) {
        log.debug("Request to get {}: {}", "{Entity}", id);
        return repository.findById(id)
            .map(this::toDTO)
            .orElseThrow(() -> new ResourceNotFoundException("{Entity} not found"));
    }
    
    // 其他方法...
}
```

### 单元测试模板

```java
@ExtendWith(MockitoExtension.class)
class {Entity}ServiceImplTest {
    @Mock
    private {Entity}Repository repository;
    
    @InjectMocks
    private {Entity}ServiceImpl service;
    
    @Test
    void testFindById_WhenExists_ShouldReturnDTO() {
        // Given
        Long id = 1L;
        {Entity} entity = new {Entity}();
        entity.setId(id);
        when(repository.findById(id)).thenReturn(Optional.of(entity));
        
        // When
        {Entity}DTO result = service.findById(id);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(id);
    }
    
    // 其他测试方法...
}
```

## 🔍 质量保证机制

### 代码质量自检清单

- [ ] 设计遵循DDD分层架构，符合六大设计理念
- [ ] 优先复用HillOS组件，避免重复实现
- [ ] 异常处理完善，日志符合规范，配置外部化
- [ ] 安全措施到位（输入验证、敏感信息处理）
- [ ] 资源正确管理（连接关闭、定时任务处理）
- [ ] 每个业务方法有测试用例（覆盖率≥60%）
- [ ] 公共API有JavaDoc，复杂逻辑有必要注释
- [ ] 实现了可观测性（健康检查、指标收集）
- [ ] 代码设计考虑了扩展性和可维护性
- [ ] 所有依赖使用了指定版本，无版本冲突

### 代码生成自省流程

1. **生成前思考**: 明确业务目标、技术约束、可复用组件
2. **生成中校验**: 确保分层架构、命名一致性、接口规范
3. **生成后验证**: 静态代码分析、异常处理完整性、配置外部化、测试覆盖

## 📋 执行检查清单

### 项目创建完成检查

- [ ] 项目结构符合HillOS标准
- [ ] Maven配置正确，依赖版本符合要求
- [ ] 包命名遵循com.hillstone.hillos规范
- [ ] README.md 已创建并遵循文档编写规范
- [ ] SonarQube配置文件已生成
- [ ] lombok.config配置已添加

### 代码生成完成检查

- [ ] 分层架构清晰（Interface-Application-Domain-Infrastructure）
- [ ] 领域层包含完整业务逻辑，符合DDD设计
- [ ] REST API实现符合接口规范
- [ ] 每个业务类都有对应的单元测试
- [ ] 异常处理完整，日志输出符合规范
- [ ] 配置信息外部化，支持环境变量
- [ ] 安全相关代码正确实现
