{"folders": [{"name": "Root", "path": "."}, {"name": "HillOS Demo Application", "path": "./hillos-demo-application"}, {"name": "HillOS Demo Starter", "path": "./hillos-demo-starter"}], "settings": {"java.configuration.workspaceCacheLimit": 90, "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx4G -Xms100m -Dfile.encoding=UTF-8", "java.import.maven.enabled": true, "java.import.gradle.enabled": false, "maven.view": "hierarchical", "java.configuration.maven.lifecycle.mappings": {"clean": ["clean"], "compile": ["compile"], "test": ["test"], "package": ["package"], "install": ["install"], "deploy": ["deploy"]}, "java.compile.nullAnalysis.mode": "automatic", "java.configuration.updateBuildConfiguration": "automatic", "java.saveActions.organizeImports": true}, "extensions": {"recommendations": ["redhat.java", "vscjava.vscode-java-debug", "vscjava.vscode-java-test", "vscjava.vscode-maven", "vscjava.vscode-java-dependency", "vscjava.vscode-java-pack", "vmware.vscode-spring-boot", "vmware.vscode-boot-dev-pack", "pflannery.vscode-versionlens"]}}